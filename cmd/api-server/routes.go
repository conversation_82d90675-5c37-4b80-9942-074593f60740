package main

import (
	"context"
	"database/sql"
	"errors"
	"html/template"
	"net/http"
	"os"
	"time"

	"api-server/configs"
	"api-server/internal/dto"
	"api-server/internal/entities"
	"api-server/internal/enums"
	"api-server/internal/gateways/aws"
	"api-server/internal/gateways/gitlab"
	"api-server/internal/gateways/mail"
	supabase "api-server/internal/gateways/supabase"
	"api-server/internal/handlers"
	"api-server/internal/middlewares"
	repository "api-server/internal/repositories"
	accesstoken_usecase "api-server/internal/usecase/access_token"
	ecr_usecase "api-server/internal/usecase/ecr"
	hardware_usecase "api-server/internal/usecase/hardware"
	healthcheck_usecase "api-server/internal/usecase/healthcheck"
	kompose_usecase "api-server/internal/usecase/kompose"
	org_accesstoken_usecase "api-server/internal/usecase/org_access_token"
	org_usecase "api-server/internal/usecase/organization"
	repo_usecase "api-server/internal/usecase/repository"
	signup_request_usecase "api-server/internal/usecase/signup_request"
	sshkey_usecase "api-server/internal/usecase/ssh_key"
	user_usecase "api-server/internal/usecase/user"
	workflow_usecase "api-server/internal/usecase/workflow"
	"api-server/internal/utils"
	"api-server/pkg/argo"
	"api-server/pkg/kube"
	"api-server/pkg/otelzap"

	"github.com/gin-gonic/gin"
	swaggerfiles "github.com/swaggo/files"
	ginSwagger "github.com/swaggo/gin-swagger"
	"go.opentelemetry.io/contrib/instrumentation/github.com/gin-gonic/gin/otelgin"
	"go.opentelemetry.io/otel/propagation"
	"go.uber.org/zap"
	"gorm.io/gorm"
)

// nolint
func InitRouter(ctx context.Context, config *configs.GlobalConfig, db gorm.ConnPool) *gin.Engine {
	r := gin.New()
	r.Use(gin.Recovery())
	r.Use(otelgin.Middleware(os.Getenv("OTEL_SERVICE_NAME"), otelgin.WithPropagators(propagation.TraceContext{})))
	r.Use(middlewares.Logging())
	r.Use(middlewares.NewCORSConfig(config.Cors))

	htmlTemplates(r, config)

	// swagger
	r.GET("/api/v1/swagger/*any", ginSwagger.WrapHandler(swaggerfiles.Handler, ginSwagger.URL("/api/v1/swagger/doc.json")))

	// repository
	repo := repository.NewRepository(db)

	// gilab client
	gitlabClient, err := gitlab.New(
		config.Gitlab.Host,
		gitlab.WithCustomGitlabSshHost(config.Gitlab.CustomSSHHost),
	)
	if err != nil {
		otelzap.Logger.Error("failed to create gitlab client", zap.Error(err))
	}

	// Supabase client
	supabaseClient, err := supabase.New(config.Supabase.Host, config.Supabase.ServiceToken, config.Supabase.InviteRedirectToURL)
	if err != nil {
		otelzap.Logger.Fatal("failed to create Supabase client", zap.Error(err))
	}

	// Mail client
	mailClient := mail.New(mail.EmailParams{
		Host:     config.Mail.Host,
		Port:     config.Mail.Port,
		Username: config.Mail.Username,
		Password: config.Mail.Password,
		From:     config.Mail.From,
	})

	//aws
	awsClient, err := aws.New(config.AwsConfig)
	if err != nil {
		otelzap.Logger.Error("failed to create aws client", zap.Error(err))
	}

	kubeClient, err := kube.New(config.InCluster)
	if err != nil {
		otelzap.Logger.Fatal("failed to create Kubernetes client", zap.Error(err))
	}

	// Argo Workflow client
	argoClient, err := argo.New(config.ArgoWorkflow.Host, config.ArgoWorkflow.AccessToken)
	if err != nil {
		otelzap.Logger.Fatal("create Argo Workflow Client failed", zap.Error(err))
	}

	// usercases
	workflowUsecase := workflow_usecase.New(config.ArgoWorkflow, repo, argoClient)
	composeUsecase := kompose_usecase.New(config, repo, kubeClient, gitlabClient)
	userUsecase := user_usecase.New(repo, gitlabClient, supabaseClient, awsClient, kubeClient, workflowUsecase, composeUsecase)
	accessTokenUsecase := accesstoken_usecase.New(repo)
	orgAccessTokenUsecase := org_accesstoken_usecase.New(repo)
	userKeyUseCase := sshkey_usecase.New(repo, gitlabClient)
	organizationUseCase := org_usecase.New(config, repo, gitlabClient, mailClient, awsClient)
	repositoryUsercase := repo_usecase.New(
		config,
		repo,
		gitlabClient,
		workflowUsecase,
		composeUsecase,
		kubeClient,
		awsClient,
	)
	signupRequest := signup_request_usecase.New(
		config,
		repo,
		gitlabClient,
		supabaseClient,
		config.Auth.PasswordEncryptSecret,
		mailClient,
	)
	hardwareUsecase := hardware_usecase.New(repo, kubeClient)
	healthcheckUsecase := healthcheck_usecase.New(gitlabClient, db.(*sql.DB))
	// Initialize ECR usecase
	ecrUsecase := ecr_usecase.New(config, awsClient, repo, kubeClient)

	// handlers
	userHandler := handlers.NewUserUserHandler(config, userUsecase)
	userKeysHandler := handlers.NewSshKeyHandler(userKeyUseCase)
	organizationHandler := handlers.NewOrganizationHandler(config, organizationUseCase)
	repositoryHandler := handlers.NewRepositoryHandler(config, repositoryUsercase, composeUsecase)
	accessTokenHandler := handlers.NewAccessTokenHandler(accessTokenUsecase)
	orgAccessTokenHandler := handlers.NewOrgAccessTokenHandler(orgAccessTokenUsecase)
	signupRequestHandler := handlers.NewSignUpRequestHandler(signupRequest)
	hardwareHandler := handlers.NewHardwareHandler(config, hardwareUsecase)
	healthCheckHandler := handlers.NewHealthCheckHandler(healthcheckUsecase)
	ecrHandler := handlers.NewECRHandler(config, ecrUsecase, composeUsecase)
	// middlewares
	bearerAuth := middlewares.BearerAuth(config.Auth.JwtSecret, userUsecase)
	tokenAuthOnQuery := middlewares.TokenAuthOnQuery(config.Auth.JwtSecret)
	accessTokenAuth := middlewares.AccessTokenAuth(userUsecase, organizationUseCase)
	isAdminMiddleware := middlewares.IsAdmin(userUsecase)
	platformPermission := middlewares.PlatformPermission(userUsecase)
	orgPermission := middlewares.OrgPermission(userUsecase)
	repoPermission := middlewares.RepoPermission(userUsecase)
	ecrPermission := middlewares.ECRPermission(userUsecase)

	// check connection to Gitlab, database
	healthcheckUsecase.Ping(ctx)
	// check Admin's Gitlab Access Token
	go checkAccessToken(ctx, repo.FindUser, gitlabClient.Health)
	// Refresh admin access token
	go refreshAdminAccessToken(ctx, signupRequest.RefreshAccessToken)

	r.GET("/explore", healthCheckHandler.Explore)
	v1 := r.Group("/api/v1")
	{
		v1.POST("/signup", signupRequestHandler.Signup)
		v1.POST("/send-confirm-email", signupRequestHandler.SendConfirmEmail)

		// users
		InitUser(
			v1,
			userHandler,
			userKeysHandler,
			accessTokenHandler,
			bearerAuth,
			platformPermission,
			isAdminMiddleware,
		)

		// repositories
		InitRepositories(
			v1,
			repositoryHandler,
			bearerAuth,
			tokenAuthOnQuery,
			repoPermission,
			platformPermission,
			accessTokenAuth,
			isAdminMiddleware,
		)

		// organizations
		InitOrganization(v1, organizationHandler, orgAccessTokenHandler, bearerAuth, orgPermission, platformPermission)

		// machine
		InitMachine(v1, hardwareHandler, bearerAuth, platformPermission)

		// signups
		InitSignups(v1, signupRequestHandler, bearerAuth, platformPermission, isAdminMiddleware)

		// ECR
		InitECR(v1, ecrHandler, bearerAuth, tokenAuthOnQuery, platformPermission, ecrPermission)
	}

	r.NoRoute(func(c *gin.Context) {
		c.JSON(
			http.StatusNotFound,
			dto.NewHTTPError(http.StatusNotFound, errors.New("Page not found")),
		)
	})

	return r
}

func htmlTemplates(r *gin.Engine, cfg *configs.GlobalConfig) {
	r.LoadHTMLFiles("static/user_invite.html", "static/password_recovery.html", "static/user_confirm.html")

	r.GET("/static/:filename", func(c *gin.Context) {
		supportEmail := cfg.SupportEmail
		platformName := cfg.PlatformName

		signUpLink := `<a href="{{ .ConfirmationURL }}">Sign up link</a>`
		verboseConfirmationURL := `<a href="{{ .ConfirmationURL }}">{{ .ConfirmationURL }}</a>`
		siteURL := `<a href="{{ .SiteURL }}">this page</a>`
		resetPassword := `<a href="{{ .ConfirmationURL }}">Reset Password</a>`
		confirmLink := `<a href="{{ .ConfirmationURL }}">Confirm email link</a>`
		userEmail := `<a href="{{ .Email }}">{{ .Email }}</a>`

		c.HTML(200, c.Param("filename"), gin.H{
			"platformName":           platformName,
			"supportEmail":           supportEmail,
			"signUpLink":             template.HTML(signUpLink),
			"verboseConfirmationURL": template.HTML(verboseConfirmationURL),
			"siteURL":                template.HTML(siteURL),
			"resetPassword":          template.HTML(resetPassword),
			"confirmLink":            template.HTML(confirmLink),
			"userEmail":              template.HTML(userEmail),
		})
	})
}

func refreshAdminAccessToken(
	ctx context.Context,
	refreshAccessToken func(ctx context.Context, userRefGitID int64) error,
) {
	ticker := time.NewTicker(time.Hour * 24 * 30) // default 30 days
	go func() {
		for {
			select {
			case <-ticker.C:
			retryRefreshLoop: // retry refresh admin until success
				for {
					err := refreshAccessToken(ctx, enums.AdminRefGitID)
					if err != nil {
						otelzap.Logger.Error(
							"Refresh admin access token",
							zap.Error(err),
						)
						otelzap.Logger.Info("Retry refresh admin access token in 10 seconds")
						utils.Sleep(ctx, 10*time.Second)
						continue retryRefreshLoop
					}

					otelzap.Logger.Info("Refresh admin access token successfully")
					break retryRefreshLoop
				}
			case <-ctx.Done():
				ticker.Stop()
				return
			}
		}
	}()
}

func checkAccessToken(
	c context.Context,
	findUser func(ctx context.Context, filters repository.FindUserFilter) (*entities.User, error),
	health func(ctx context.Context, token string) (bool, error),
) {
	adminRefGitID := enums.AdminRefGitID
	adminUser, err := findUser(c, repository.FindUserFilter{RefGitUserID: &adminRefGitID})
	if err != nil {
		otelzap.Logger.Fatal("get admin user error", zap.Error(err))
	}

	validToken, err := health(c, *adminUser.GitlabAccessToken)
	if !validToken {
		otelzap.Logger.Fatal("invalid token", zap.Error(err))
	}
}

func InitMachine(router *gin.RouterGroup, c handlers.HardwareHandler, bearerAuth gin.HandlerFunc, platformPermission middlewares.PlatformPermissionHandlerFunc) {
	// machines
	machines := router.Group("/hardwares")
	{
		machines.GET("", bearerAuth, platformPermission(enums.AppPermission_SpacesRead), c.ListGPUNodes)
		// machines.GET("", c.ListMachines)
		// machines.POST("", c.RegisterMachine)
		// machines.DELETE(":host", c.DeregisterMachine)
	}
}

// nolint
func InitUser(
	router *gin.RouterGroup,
	userHandler handlers.UserHandler,
	userKeyHandler handlers.SshKeysHandler,
	accessTokenHandler handlers.AccessTokenHandler,
	bearerAuth gin.HandlerFunc,
	platformPermission middlewares.PlatformPermissionHandlerFunc,
	isAdminMiddleware gin.HandlerFunc,
) {
	users := router.Group("/users")
	{
		users.GET(
			"",
			bearerAuth,
			platformPermission(enums.AppPermission_UsersRead),
			userHandler.ListUsers,
		)
		users.PATCH(
			"/:user_id/role",
			bearerAuth,
			platformPermission(enums.AppPermission_UsersEdit),
			isAdminMiddleware,
			userHandler.UpdateUserRole,
		)
		users.GET("/me", bearerAuth, userHandler.GetCurrentUser)
		users.PUT("/me/password", bearerAuth, userHandler.ChangePassword)
		users.DELETE(
			"/:user_id",
			bearerAuth,
			platformPermission(enums.AppPermission_UsersDelete),
			isAdminMiddleware,
			userHandler.DeleteUser,
		)

		// ssh keys
		users.GET(
			"/keys",
			bearerAuth,
			platformPermission(enums.AppPermission_KeysRead),
			userKeyHandler.ListSshKeys,
		)
		users.POST(
			"/keys",
			bearerAuth,
			platformPermission(enums.AppPermission_KeysEdit),
			userKeyHandler.AddNewSshKey,
		)
		users.GET(
			"/keys/:key_id",
			bearerAuth,
			platformPermission(enums.AppPermission_KeysRead),
			userKeyHandler.GetSshKey,
		)
		users.DELETE(
			"/keys/:key_id",
			bearerAuth,
			platformPermission(enums.AppPermission_KeysDelete),
			userKeyHandler.DeleteSshKey,
		)

		// access token
		users.GET(
			"/access_tokens",
			bearerAuth,
			platformPermission(enums.AppPermission_KeysRead),
			accessTokenHandler.ListAccessToken,
		)
		users.POST(
			"/access_tokens",
			bearerAuth,
			platformPermission(enums.AppPermission_KeysEdit),
			accessTokenHandler.CreateNewAccessToken,
		)
		users.DELETE(
			"/access_tokens/:id",
			bearerAuth,
			platformPermission(enums.AppPermission_KeysDelete),
			accessTokenHandler.DeleteAccessToken,
		)
		users.POST(
			"/access_tokens/verify",
			bearerAuth,
			platformPermission(enums.AppPermission_KeysRead),
			accessTokenHandler.VerifyAccessToken,
		)

		//avatar
		users.POST("/:user_id/avatar", bearerAuth, userHandler.UploadUserAvatar)
		users.DELETE("/:user_id/avatar", bearerAuth, userHandler.DeleteUserAvatar)
	}
}

// nolint
func InitOrganization(
	router *gin.RouterGroup,
	organizationHandler handlers.OrganizationHandler,
	orgAccessTokenHandler handlers.OrgAccessTokenHandler,
	bearerAuth gin.HandlerFunc,
	orgPermission middlewares.OrgPermissionHandlerFunc,
	platformPermission middlewares.PlatformPermissionHandlerFunc,
) {
	org := router.Group("/organizations")
	{
		org.POST(
			"",
			bearerAuth,
			platformPermission(enums.AppPermission_OrgsRead),
			organizationHandler.CreateOrganization,
		)
		org.PUT(
			"/:org_id",
			bearerAuth,
			platformPermission(enums.AppPermission_OrgsEdit),
			orgPermission(enums.RepoPermission_Edit),
			organizationHandler.UpdateOrganization,
		)
		org.GET(
			"/:org_id/members/:member_id",
			bearerAuth,
			platformPermission(enums.AppPermission_OrgsRead),
			organizationHandler.GetMember,
		)
		org.DELETE(
			"/:org_id/members/:member_id",
			bearerAuth,
			platformPermission(enums.AppPermission_OrgsEdit),
			orgPermission(enums.RepoPermission_Edit, enums.RepoPermission_MembersDelete),
			organizationHandler.RemoveMember,
		)
		org.PUT(
			"/:org_id/members/:member_id",
			bearerAuth,
			platformPermission(enums.AppPermission_OrgsEdit),
			orgPermission(enums.RepoPermission_Edit, enums.RepoPermission_MembersEdit),
			organizationHandler.UpdateMember,
		)
		org.DELETE(
			"/:org_id",
			bearerAuth,
			platformPermission(enums.AppPermission_OrgsDelete),
			orgPermission(enums.RepoPermission_Delete),
			organizationHandler.DeleteOrganization,
		)
		org.POST(
			"/:org_id/invite",
			bearerAuth,
			platformPermission(enums.AppPermission_OrgsEdit),
			orgPermission(enums.RepoPermission_Edit, enums.RepoPermission_MembersEdit),
			organizationHandler.InviteUsers,
		)
		org.GET(
			"/me",
			bearerAuth,
			platformPermission(enums.AppPermission_OrgsRead),
			organizationHandler.ListCurrentUserOrganizations,
		)
		org.GET(
			"/:org_id",
			bearerAuth,
			platformPermission(enums.AppPermission_OrgsRead),
			organizationHandler.GetOrganization,
		)
		org.GET(
			"/:org_id/members",
			bearerAuth,
			platformPermission(enums.AppPermission_OrgsRead),
			organizationHandler.ListOrganizationMembers,
		)
		org.GET(
			"",
			bearerAuth,
			platformPermission(enums.AppPermission_OrgsRead),
			organizationHandler.ListAllOrganizations,
		)

		//avatar
		org.POST(
			"/:org_id/avatar",
			bearerAuth,
			platformPermission(enums.AppPermission_OrgsRead, enums.AppPermission_OrgsEdit),
			organizationHandler.UploadOrganizationAvatar,
		)
		org.DELETE(
			"/:org_id/avatar",
			bearerAuth,
			platformPermission(enums.AppPermission_OrgsRead, enums.AppPermission_OrgsEdit),
			organizationHandler.DeleteOrganizationAvatar,
		)

		// access token
		org.GET(
			"/:org_id/access_tokens",
			bearerAuth,
			platformPermission(enums.AppPermission_KeysRead),
			orgAccessTokenHandler.ListOrgAccessToken,
		)
		org.POST(
			"/:org_id/access_tokens",
			bearerAuth,
			platformPermission(enums.AppPermission_KeysEdit),
			orgAccessTokenHandler.CreateNewOrgAccessToken,
		)
		org.DELETE(
			"/:org_id/access_tokens/:id",
			bearerAuth,
			platformPermission(enums.AppPermission_KeysDelete),
			orgAccessTokenHandler.DeleteOrgAccessToken,
		)
		org.POST(
			"/access_tokens/verify",
			bearerAuth,
			platformPermission(enums.AppPermission_KeysRead),
			orgAccessTokenHandler.VerifyOrgAccessToken,
		)
	}
}

// nolint
func InitRepositories(
	router *gin.RouterGroup,
	repositoryHandler handlers.RepositoryHandler,
	bearerAuth gin.HandlerFunc,
	tokenAuthOnQuery gin.HandlerFunc,
	repoPermission middlewares.RepoPermissionHandlerFunc,
	platformPermission middlewares.PlatformPermissionHandlerFunc,
	accessTokenAuth gin.HandlerFunc,
	isAdminMiddleware gin.HandlerFunc,
) {
	repo := router.Group("/repositories")
	{
		repo.POST(
			"",
			bearerAuth,
			platformPermission(
				enums.AppPermission_SpacesAdd,
				enums.AppPermission_ModelsAdd,
				enums.AppPermission_DatasetsAdd,
			),
			repositoryHandler.AddRepo,
		)
		repo.GET(
			"",
			bearerAuth,
			platformPermission(
				enums.AppPermission_SpacesRead,
				enums.AppPermission_ModelsRead,
				enums.AppPermission_DatasetsRead,
			),
			repositoryHandler.ListRepos,
		)

		repo.POST(
			"/deployments/status",
			repositoryHandler.UpdateDeploymentStatus,
		) //TODO: need to secure

		//repository tags
		repo.GET("/tags", bearerAuth, repositoryHandler.ListRepoTags)
		repo.POST("/tags", bearerAuth, isAdminMiddleware, repositoryHandler.CreateRepoTag)
		repo.PUT("/tags/:tag_id", bearerAuth, isAdminMiddleware, repositoryHandler.UpdateRepoTag)
		repo.GET("/tags/:tag_id", bearerAuth, repositoryHandler.GetRepoTag)
		repo.DELETE("/tags/:tag_id", bearerAuth, isAdminMiddleware, repositoryHandler.DeleteRepoTag)

		repo.GET("/templates", bearerAuth, repositoryHandler.ListRepoTemplates)
		repo.POST(
			"/gitlab/webhook/push_event",
			repositoryHandler.PushEventWebhookHandler,
		) //TODO: need to secure

		repo.GET("/deployments", bearerAuth, repositoryHandler.ListDeployments)
	}

	r := router.Group("/repositories/:repo_type/:namespace/:repo_name")
	{
		// repo
		r.DELETE(
			"",
			bearerAuth,
			platformPermission(enums.AppPermission_SpacesRead, enums.AppPermission_ModelsRead, enums.AppPermission_DatasetsRead),
			repoPermission(enums.RepoPermission_Delete),
			repositoryHandler.DeleteRepo,
		)
		r.PATCH(
			"",
			bearerAuth,
			platformPermission(enums.AppPermission_SpacesRead, enums.AppPermission_ModelsRead, enums.AppPermission_DatasetsRead),
			repoPermission(enums.RepoPermission_Edit),
			repositoryHandler.UpdateRepo,
		)
		r.GET(
			"",
			bearerAuth,
			platformPermission(
				enums.AppPermission_SpacesRead,
				enums.AppPermission_ModelsRead,
				enums.AppPermission_DatasetsRead,
			),
			repositoryHandler.GetRepoInfo,
		)
		r.GET(
			"/archive",
			bearerAuth,
			platformPermission(
				enums.AppPermission_SpacesRead,
				enums.AppPermission_ModelsRead,
				enums.AppPermission_DatasetsRead,
			),
			repositoryHandler.ArchiveRepository,
		)

		// avatar
		r.POST(
			"/avatar",
			bearerAuth,
			platformPermission(
				enums.AppPermission_SpacesRead,
				enums.AppPermission_ModelsRead,
				enums.AppPermission_DatasetsRead,
			),
			repoPermission(enums.RepoPermission_Edit),
			repositoryHandler.UploadRepoAvatar,
		)
		r.DELETE(
			"/avatar",
			bearerAuth,
			platformPermission(
				enums.AppPermission_SpacesRead,
				enums.AppPermission_ModelsRead,
				enums.AppPermission_DatasetsRead,
			),
			repoPermission(enums.RepoPermission_Edit),
			repositoryHandler.DeleteRepoAvatar,
		)

		// contributors
		r.GET(
			"/contributors",
			bearerAuth,
			platformPermission(
				enums.AppPermission_SpacesRead,
				enums.AppPermission_ModelsRead,
				enums.AppPermission_DatasetsRead,
			),
			repositoryHandler.ListRepoContributors,
		)
		r.POST(
			"/invite",
			bearerAuth,
			platformPermission(
				enums.AppPermission_SpacesRead,
				enums.AppPermission_ModelsRead,
				enums.AppPermission_DatasetsRead,
			),
			repoPermission(enums.RepoPermission_Edit, enums.RepoPermission_MembersEdit),
			repositoryHandler.InviteUsers,
		)
		r.GET(
			"/members",
			bearerAuth,
			platformPermission(
				enums.AppPermission_SpacesRead,
				enums.AppPermission_ModelsRead,
				enums.AppPermission_DatasetsRead,
			),
			repositoryHandler.ListRepoMembers,
		)
		r.GET(
			"/members/:member_id",
			bearerAuth,
			platformPermission(
				enums.AppPermission_SpacesRead,
				enums.AppPermission_ModelsRead,
				enums.AppPermission_DatasetsRead,
			),
			repositoryHandler.GetRepositoryMember,
		)
		r.DELETE(
			"/members/:member_id",
			bearerAuth,
			platformPermission(
				enums.AppPermission_SpacesRead,
				enums.AppPermission_ModelsRead,
				enums.AppPermission_DatasetsRead,
			),
			repoPermission(enums.RepoPermission_Edit, enums.RepoPermission_MembersDelete),
			repositoryHandler.RemoveRepositoryMember,
		)
		r.PUT(
			"/members/:member_id",
			bearerAuth,
			platformPermission(
				enums.AppPermission_SpacesRead,
				enums.AppPermission_ModelsRead,
				enums.AppPermission_DatasetsRead,
			),
			repoPermission(enums.RepoPermission_Edit, enums.RepoPermission_MembersEdit),
			repositoryHandler.UpdateRepositoryMember,
		)

		// env
		r.GET(
			"/envs",
			bearerAuth,
			repoPermission(enums.RepoPermission_Edit),
			repositoryHandler.GetEnv,
		)
		r.POST(
			"/envs",
			bearerAuth,
			repoPermission(enums.RepoPermission_Edit),
			repositoryHandler.CreateEnv,
		)
		r.PUT(
			"/envs",
			bearerAuth,
			repoPermission(enums.RepoPermission_Edit),
			repositoryHandler.UpdateEnv,
		)
		r.DELETE(
			"/envs",
			bearerAuth,
			repoPermission(enums.RepoPermission_Edit),
			repositoryHandler.DeleteEnv,
		)

		// deployment
		r.POST(
			"/deployments/start",
			bearerAuth,
			repoPermission(enums.RepoPermission_DeployRun),
			repositoryHandler.StartDeployment,
		)
		r.POST(
			"/deployments/rebuild",
			bearerAuth,
			repoPermission(enums.RepoPermission_DeployRun),
			repositoryHandler.StartDeployment,
		)
		r.GET(
			"/deployments/restart",
			bearerAuth,
			repoPermission(enums.RepoPermission_DeployRun),
			repositoryHandler.RestartDeployment,
		)
		r.POST(
			"/deployments/stop",
			bearerAuth,
			repoPermission(enums.RepoPermission_DeployStop),
			repositoryHandler.StopDeployment,
		)
		r.GET("/deployments/logs", tokenAuthOnQuery, repositoryHandler.GetDeploymentBuildLogs)
		r.GET("/deployments/pods/logs", tokenAuthOnQuery, repositoryHandler.GetDeploymentPodLogs)
		r.GET("/deployments/status", bearerAuth, repositoryHandler.GetDeploymentStatus)
		r.POST(
			"/deployments/terminate",
			bearerAuth,
			repoPermission(enums.RepoPermission_DeployStop),
			repositoryHandler.StopDeployment,
		)
		r.GET("/composes", bearerAuth, repositoryHandler.GetComposeServices)
		r.GET("/pvcs", bearerAuth, repositoryHandler.ListEFS)

		// commit
		r.POST("/commits", accessTokenAuth, repoPermission(enums.RepoPermission_DeployRun), repositoryHandler.CreateRepoCommit)
		r.GET(
			"/commits",
			bearerAuth,
			platformPermission(
				enums.AppPermission_SpacesRead,
				enums.AppPermission_ModelsRead,
				enums.AppPermission_DatasetsRead,
			),
			repositoryHandler.ListRepoCommits,
		)

		// files
		r.GET(
			"/files",
			bearerAuth,
			platformPermission(
				enums.AppPermission_SpacesFilesRead,
				enums.AppPermission_ModelsFilesRead,
				enums.AppPermission_DatasetsFilesRead,
			),
			repositoryHandler.ListRepoFiles,
		)
		r.GET(
			"/files/*file_path",
			bearerAuth,
			platformPermission(
				enums.AppPermission_SpacesFilesRead,
				enums.AppPermission_ModelsFilesRead,
				enums.AppPermission_DatasetsFilesRead,
			),
			repositoryHandler.GetFileContent,
		)
		r.GET(
			"/readme",
			bearerAuth,
			repositoryHandler.GetReadmeFileContent,
		)
		r.HEAD("/raw/*file_path",
			bearerAuth,
			platformPermission(
				enums.AppPermission_SpacesFilesRead,
				enums.AppPermission_ModelsFilesRead,
				enums.AppPermission_DatasetsFilesRead,
			),
			repositoryHandler.GetHeaderRawFileContent,
		)
		r.GET("/raw/*file_path",
			bearerAuth,
			platformPermission(
				enums.AppPermission_SpacesFilesRead,
				enums.AppPermission_ModelsFilesRead,
				enums.AppPermission_DatasetsFilesRead,
			),
			repositoryHandler.GetRawFileContent,
		)

		// branch
		r.GET("/branches/:branch", bearerAuth, repositoryHandler.GetSingleRepoBranch)
		r.GET(
			"/branches",
			bearerAuth,
			platformPermission(
				enums.AppPermission_SpacesRead,
				enums.AppPermission_ModelsRead,
				enums.AppPermission_DatasetsRead,
			),
			repositoryHandler.ListRepoBranches,
		)

		// access tokens
		// r.POST("/access_tokens", bearerAuth, repositoryHandler.CreateRepoAccessToken)
		// r.GET("/access_tokens", bearerAuth, repositoryHandler.ListRepoAccessToken)
		// r.DELETE("/access_tokens/:id", bearerAuth, repositoryHandler.DeleteRepoAccessToken)
	}
}

func InitECR(
	router *gin.RouterGroup,
	ecrHandler handlers.ECRHandler,
	bearerAuth gin.HandlerFunc,
	tokenAuthOnQuery gin.HandlerFunc,
	platformPermission middlewares.PlatformPermissionHandlerFunc,
	ecrPermission middlewares.ECRPermissionHandlerFunc,
) {
	ecr := router.Group("/ecr")
	{
		// ecr.GET("/repositories", bearerAuth, ecrHandler.ListRepositories)
		// ecr.GET("/repositories/:id", bearerAuth, ecrHandler.GetRepository)
		// ecr.POST("/repositories", bearerAuth, ecrHandler.CreateRepository)
		// ecr.DELETE("/repositories/:id", bearerAuth, ecrHandler.DeleteRepository)
		// ecr.GET("/repositories/:id/images", bearerAuth, ecrHandler.ListImages)
		// ecr.GET("/repositories/:id/authorization-token", bearerAuth, ecrHandler.GetAuthorizationToken)

		// ECR deployments
		ecr.GET("/deployments/:ecr_id", bearerAuth, platformPermission(enums.AppPermission_ECRRead), ecrHandler.GetECRDeployment)
		ecr.GET("/deployments", bearerAuth, platformPermission(enums.AppPermission_ECRRead), ecrHandler.ListECRDeployment)
		ecr.POST("/deployments", bearerAuth, platformPermission(enums.AppPermission_ECREdit), ecrHandler.CreateECRDeployment)
		ecr.PUT("/deployments/:ecr_id", bearerAuth, platformPermission(enums.AppPermission_ECREdit), ecrPermission(), ecrHandler.UpdateECRDeployment)
		ecr.DELETE("/deployments/:ecr_id", bearerAuth, platformPermission(enums.AppPermission_ECRDelete), ecrPermission(), ecrHandler.DeleteECRDeployment)

		ecr.GET("/deployments/:ecr_id/status", bearerAuth, ecrHandler.GetDeploymentStatus)
		ecr.GET("/deployments/:ecr_id/logs", tokenAuthOnQuery, ecrHandler.GetDeploymentPodLogs)

		ecr.POST("/deployments/:ecr_id/deploy", bearerAuth, platformPermission(enums.AppPermission_ECREdit), ecrPermission(), ecrHandler.DeployECR)
		ecr.POST("/deployments/:ecr_id/stop", bearerAuth, platformPermission(enums.AppPermission_ECREdit), ecrPermission(), ecrHandler.StopECRDeployment)
		ecr.POST("/deployments/:ecr_id/env", bearerAuth, platformPermission(enums.AppPermission_ECREdit), ecrPermission(), ecrHandler.CreateECRDeploymentEnv)
		ecr.PUT("/deployments/:ecr_id/env", bearerAuth, platformPermission(enums.AppPermission_ECREdit), ecrPermission(), ecrHandler.UpdateECRDeploymentEnv)
		ecr.DELETE("/deployments/:ecr_id/env", bearerAuth, platformPermission(enums.AppPermission_ECREdit), ecrPermission(), ecrHandler.DeleteECRDeploymentEnv)

	}

	// Compose
	composes := router.Group("/composes")
	{
		composes.GET("/deployments", bearerAuth, platformPermission(enums.AppPermission_ECRRead), ecrHandler.ListComposeDeployment)
	}
}

func InitSignups(
	router *gin.RouterGroup,
	signupRequestHandler handlers.SignUpRequestHandler,
	bearerAuth gin.HandlerFunc,
	platformPermission middlewares.PlatformPermissionHandlerFunc,
	isAdminMiddleware gin.HandlerFunc,
) {
	signups := router.Group("/admin")
	{
		signups.GET(
			"/signups",
			bearerAuth,
			platformPermission(enums.AppPermission_UsersApprove, enums.AppPermission_UsersRead),
			isAdminMiddleware,
			signupRequestHandler.ListApprovalRequest,
		)
		signups.PATCH(
			"/signups/:id/approval",
			bearerAuth,
			platformPermission(enums.AppPermission_UsersApprove),
			isAdminMiddleware,
			signupRequestHandler.ProcessSignUpRequest,
		)
		signups.POST(
			"/invite",
			bearerAuth,
			platformPermission(enums.AppPermission_UsersInvite),
			isAdminMiddleware,
			signupRequestHandler.InviteUser,
		)
	}
}
