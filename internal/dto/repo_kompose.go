package dto

import (
	"time"

	"github.com/google/uuid"

	"api-server/internal/entities"
	"api-server/internal/types"
	"api-server/internal/utils"
)

type ComposeService struct {
	ID            uuid.UUID `json:"id"`
	Name          string    `json:"name"`
	Author        string    `json:"author"`
	Image         string    `json:"image"`
	Ports         []string  `json:"ports"`
	Volumes       []string  `json:"volumes"`
	Status        string    `json:"status"`
	RestartPolicy string    `json:"restart_policy"`

	RepoID        types.RepoID `json:"repo_id"`
	URL           string       `json:"url"`
	PrivateURL    string       `json:"private_url"`
	NodeName      string       `json:"node_name"`
	ProxyBodySize int          `json:"proxy_body_size"` // in megabytes
	CreatedAt     time.Time    `json:"created_at"`
	UpdatedAt     time.Time    `json:"updated_at"`
}

var _ FromEntity[entities.CustomImageDeployment, ComposeService] = (*ComposeService)(nil)

func (u ComposeService) FromEntity(e entities.CustomImageDeployment) ComposeService {
	u.ID = e.ID
	u.Name = e.DeploymentName

	if e.User != nil {
		u.Author = e.User.Username
	} else if e.Author != nil {
		u.Author = *e.Author
	} else {
		u.Author = "unknown"
	}

	u.Image = e.ImageURI
	u.Ports = e.ComposePorts
	u.Volumes = e.Volumes

	u.Status = "Running"

	if e.RestartPolicy == nil {
		u.RestartPolicy = "always"
	} else {
		u.RestartPolicy = *e.RestartPolicy
	}

	if e.Namespace != nil {
		u.PrivateURL = utils.GetPrivateDeploymentURL(e.DeploymentName, *e.Namespace)
	}

	if e.Repo != nil {
		if e.Repo.User != nil {
			u.RepoID = *types.NewRepoID(e.Repo.Type, e.Repo.User.Username, e.Repo.Name)
		} else if e.Repo.Org != nil {
			u.RepoID = *types.NewRepoID(e.Repo.Type, e.Repo.Org.PathName, e.Repo.Name)
		}
	}

	u.ProxyBodySize = e.ProxyBodySize
	u.NodeName = e.NodeName
	u.CreatedAt = e.CreatedAt
	u.UpdatedAt = e.UpdatedAt

	return u
}

type GetComposeServicesRequest struct {
	RepoID types.RepoID
	Search *string `form:"search,omitempty" validate:"omitempty,min=1,max=100,keyword" binding:"omitempty,min=1,max=100"`
	PaginateRequest
}

type GetComposeServicesResponse HTTPResponse[[]ComposeService]

// EFS represents an EFS (Elastic File System) PVC in Kubernetes
type EFS struct {
	Name         string    `json:"name"`
	Namespace    string    `json:"namespace"`
	StorageClass string    `json:"storage_class"`
	Size         string    `json:"size"`
	AccessModes  []string  `json:"access_modes"`
	Status       string    `json:"status"`
	CreatedAt    time.Time `json:"created_at"`
}
