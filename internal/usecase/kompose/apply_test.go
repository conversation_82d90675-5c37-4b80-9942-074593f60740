package kompose_test

import (
	"context"
	"errors"
	"fmt"
	"os"
	"testing"

	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	appsv1 "k8s.io/api/apps/v1"
	corev1 "k8s.io/api/core/v1"
	rbacv1 "k8s.io/api/rbac/v1"
	k8serrors "k8s.io/apimachinery/pkg/api/errors"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/runtime"
	"k8s.io/apimachinery/pkg/runtime/schema"
	"k8s.io/client-go/kubernetes/fake"
	k8stesting "k8s.io/client-go/testing"

	"api-server/configs"
	"api-server/internal/entities"
	"api-server/internal/enums"
	gitlabMocks "api-server/internal/gateways/gitlab/mocks"
	repoMocks "api-server/internal/repositories/mocks"
	"api-server/internal/usecase/kompose"
	"api-server/internal/utils"
)

func TestApplyKubernetesManifests(t *testing.T) {
	type dependencies struct {
		repo      *repoMocks.MockRepository
		gitlab    *gitlabMocks.MockGitlabClient
		config    *configs.GlobalConfig
		k8sClient *fake.Clientset
	}

	// Sample test data
	repoID := uuid.New()
	sampleRepo := &entities.Repository{
		BaseModel: entities.BaseModel{
			ID: repoID,
		},
		Name: "test-repo",
		Type: enums.RepoType_Composes,
	}

	// Sample Kubernetes manifests
	deploymentManifest := kompose.KubernetesManifest{
		Kind: "Deployment",
		Name: "web",
		Content: `apiVersion: apps/v1
kind: Deployment
metadata:
  name: web
  namespace: test-namespace
spec:
  replicas: 1
  selector:
    matchLabels:
      app: web
  template:
    metadata:
      labels:
        app: web
    spec:
      containers:
      - name: web
        image: nginx:latest
        ports:
        - containerPort: 80
        resources: {}`,
	}

	serviceManifest := kompose.KubernetesManifest{
		Kind: "Service",
		Name: "web-service",
		Content: `apiVersion: v1
kind: Service
metadata:
  name: web-service
  namespace: test-namespace
spec:
  selector:
    app: web
  ports:
  - port: 80
    targetPort: 80`,
	}

	pvcManifest := kompose.KubernetesManifest{
		Kind: "PersistentVolumeClaim",
		Name: "data-pvc",
		Content: `apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: data-pvc
  namespace: test-namespace
spec:
  accessModes:
  - ReadWriteOnce
  resources:
    requests:
      storage: 1Gi`,
	}

	tests := []struct {
		name           string
		input          kompose.ApplyKubernetesManifestsInput
		setupMocks     func(*dependencies)
		setupK8s       func(*fake.Clientset)
		expectedResult *kompose.ApplyKubernetesManifestsResponse
		expectedError  error
	}{
		{
			name: "successful apply with new namespace creation",
			input: kompose.ApplyKubernetesManifestsInput{
				Manifests:   []kompose.KubernetesManifest{deploymentManifest, serviceManifest},
				Namespace:   "test-namespace",
				DryRun:      false,
				Force:       false,
				ComposeRepo: sampleRepo,
				ResourceRequirements: map[string]*kompose.ResourceRequirement{
					"web": {
						Requests: &kompose.ResourceSpec{
							CPU:    utils.Ptr("100m"),
							Memory: utils.Ptr("128Mi"),
						},
					},
				},
				GPURequirements: nil,
				NonRoot:         nil,
			},
			setupMocks: func(d *dependencies) {
				// No specific mocks needed for this test
			},
			setupK8s: func(k8sClient *fake.Clientset) {
				// Simulate namespace not found initially
				k8sClient.PrependReactor("get", "namespaces", func(action k8stesting.Action) (handled bool, ret runtime.Object, err error) {
					return true, nil, k8serrors.NewNotFound(schema.GroupResource{Resource: "namespaces"}, "test-namespace")
				})

				// Create RoleBinding to simulate successful polling
				roleBinding := &rbacv1.RoleBinding{
					ObjectMeta: metav1.ObjectMeta{
						Name:      "compose-deployment-manager-role-binding",
						Namespace: "test-namespace",
					},
				}
				k8sClient.RbacV1().RoleBindings("test-namespace").Create(context.Background(), roleBinding, metav1.CreateOptions{})
			},
			expectedResult: &kompose.ApplyKubernetesManifestsResponse{
				AppliedResources: []kompose.AppliedResource{
					{
						Kind:      "Deployment",
						Name:      "web",
						Namespace: "test-namespace",
						Action:    "created",
						Message:   "deployment.apps/web created",
					},
					{
						Kind:      "Service",
						Name:      "web",
						Namespace: "test-namespace",
						Action:    "created",
						Message:   "service/web created",
					},
				},
				Summary: kompose.ApplySummary{
					TotalResources:      2,
					SuccessfulResources: 2,
					FailedResources:     0,
					Actions:             []string{"created", "created"},
					ActionsCount:        map[string]int{"created": 2},
				},
				Errors: []string{},
			},
			expectedError: nil,
		},
		{
			name: "dry run mode",
			input: kompose.ApplyKubernetesManifestsInput{
				Manifests:   []kompose.KubernetesManifest{deploymentManifest},
				Namespace:   "test-namespace",
				DryRun:      true,
				Force:       false,
				ComposeRepo: sampleRepo,
			},
			setupMocks: func(d *dependencies) {
				// No specific mocks needed for dry run
			},
			setupK8s: func(k8sClient *fake.Clientset) {
				// Create namespace to avoid creation during dry run
				namespace := &corev1.Namespace{
					ObjectMeta: metav1.ObjectMeta{
						Name: "test-namespace",
					},
				}
				k8sClient.CoreV1().Namespaces().Create(context.Background(), namespace, metav1.CreateOptions{})

				// Create RoleBinding to simulate successful polling
				roleBinding := &rbacv1.RoleBinding{
					ObjectMeta: metav1.ObjectMeta{
						Name:      "compose-deployment-manager-role-binding",
						Namespace: "test-namespace",
					},
				}
				k8sClient.RbacV1().RoleBindings("test-namespace").Create(context.Background(), roleBinding, metav1.CreateOptions{})
			},
			expectedResult: &kompose.ApplyKubernetesManifestsResponse{
				AppliedResources: []kompose.AppliedResource{
					{
						Kind:      "Deployment",
						Name:      "web",
						Namespace: "test-namespace",
						Action:    "dry-run",
						Message:   "Deployment would be created/updated (dry run)",
					},
				},
				Summary: kompose.ApplySummary{
					TotalResources:      1,
					SuccessfulResources: 1,
					FailedResources:     0,
					Actions:             []string{"dry-run"},
					ActionsCount:        map[string]int{"dry-run": 1},
				},
				Errors: []string{},
			},
			expectedError: nil,
		},
		{
			name: "apply with PVC and EFS storage class",
			input: kompose.ApplyKubernetesManifestsInput{
				Manifests:   []kompose.KubernetesManifest{pvcManifest},
				Namespace:   "test-namespace",
				DryRun:      false,
				Force:       false,
				ComposeRepo: sampleRepo,
			},
			setupMocks: func(d *dependencies) {
				// No specific mocks needed
			},
			setupK8s: func(k8sClient *fake.Clientset) {
				// Create namespace
				namespace := &corev1.Namespace{
					ObjectMeta: metav1.ObjectMeta{
						Name: "test-namespace",
					},
				}
				k8sClient.CoreV1().Namespaces().Create(context.Background(), namespace, metav1.CreateOptions{})

				// Create RoleBinding to simulate successful polling
				roleBinding := &rbacv1.RoleBinding{
					ObjectMeta: metav1.ObjectMeta{
						Name:      "compose-deployment-manager-role-binding",
						Namespace: "test-namespace",
					},
				}
				k8sClient.RbacV1().RoleBindings("test-namespace").Create(context.Background(), roleBinding, metav1.CreateOptions{})
			},
			expectedResult: &kompose.ApplyKubernetesManifestsResponse{
				AppliedResources: []kompose.AppliedResource{
					{
						Kind:      "PersistentVolumeClaim",
						Name:      "data-pvc",
						Namespace: "test-namespace",
						Action:    "created",
						Message:   "persistentvolumeclaim/data-pvc created",
					},
				},
				Summary: kompose.ApplySummary{
					TotalResources:      1,
					SuccessfulResources: 1,
					FailedResources:     0,
					Actions:             []string{"created"},
					ActionsCount:        map[string]int{"created": 1},
				},
				Errors: []string{},
			},
			expectedError: nil,
		},
		{
			name: "apply with GPU requirements",
			input: kompose.ApplyKubernetesManifestsInput{
				Manifests:   []kompose.KubernetesManifest{deploymentManifest},
				Namespace:   "test-namespace",
				DryRun:      false,
				Force:       false,
				ComposeRepo: sampleRepo,
				GPURequirements: map[string]*kompose.GPURequirement{
					"web": {
						Enabled: true,
						GPUType: utils.Ptr("Tesla-T4"),
						Tolerations: []kompose.GPUToleration{
							{
								Key:      "nvidia.com/gpu",
								Operator: "Exists",
								Effect:   "NoSchedule",
							},
						},
						NodeSelector: map[string]string{
							"nvidia.com/gpu.product": "Tesla-T4",
						},
					},
				},
			},
			setupMocks: func(d *dependencies) {
				// No specific mocks needed
			},
			setupK8s: func(k8sClient *fake.Clientset) {
				// Create namespace
				namespace := &corev1.Namespace{
					ObjectMeta: metav1.ObjectMeta{
						Name: "test-namespace",
					},
				}
				k8sClient.CoreV1().Namespaces().Create(context.Background(), namespace, metav1.CreateOptions{})

				// Create RoleBinding to simulate successful polling
				roleBinding := &rbacv1.RoleBinding{
					ObjectMeta: metav1.ObjectMeta{
						Name:      "compose-deployment-manager-role-binding",
						Namespace: "test-namespace",
					},
				}
				k8sClient.RbacV1().RoleBindings("test-namespace").Create(context.Background(), roleBinding, metav1.CreateOptions{})
			},
			expectedResult: &kompose.ApplyKubernetesManifestsResponse{
				AppliedResources: []kompose.AppliedResource{
					{
						Kind:      "Deployment",
						Name:      "web",
						Namespace: "test-namespace",
						Action:    "created",
						Message:   "deployment.apps/web created",
					},
				},
				Summary: kompose.ApplySummary{
					TotalResources:      1,
					SuccessfulResources: 1,
					FailedResources:     0,
					Actions:             []string{"created"},
					ActionsCount:        map[string]int{"created": 1},
				},
				Errors: []string{},
			},
			expectedError: nil,
		},
		{
			name: "invalid manifest YAML",
			input: kompose.ApplyKubernetesManifestsInput{
				Manifests: []kompose.KubernetesManifest{
					{
						Kind: "Deployment",
						Name: "invalid",
						Content: `invalid: yaml: content:
  - this is not valid yaml
    missing proper indentation`,
					},
				},
				Namespace:   "test-namespace",
				DryRun:      false,
				Force:       false,
				ComposeRepo: sampleRepo,
			},
			setupMocks: func(d *dependencies) {
				// No specific mocks needed
			},
			setupK8s: func(k8sClient *fake.Clientset) {
				// Create namespace
				namespace := &corev1.Namespace{
					ObjectMeta: metav1.ObjectMeta{
						Name: "test-namespace",
					},
				}
				k8sClient.CoreV1().Namespaces().Create(context.Background(), namespace, metav1.CreateOptions{})

				// Create RoleBinding to simulate successful polling
				roleBinding := &rbacv1.RoleBinding{
					ObjectMeta: metav1.ObjectMeta{
						Name:      "compose-deployment-manager-role-binding",
						Namespace: "test-namespace",
					},
				}
				k8sClient.RbacV1().RoleBindings("test-namespace").Create(context.Background(), roleBinding, metav1.CreateOptions{})
			},
			expectedResult: &kompose.ApplyKubernetesManifestsResponse{
				AppliedResources: []kompose.AppliedResource{},
				Summary: kompose.ApplySummary{
					TotalResources:      1,
					SuccessfulResources: 0,
					FailedResources:     1,
					Actions:             []string{},
					ActionsCount:        map[string]int{},
				},
				Errors: []string{"Failed to apply Deployment/invalid: failed to parse YAML: error converting YAML to JSON: yaml: line 2: mapping values are not allowed in this context"},
			},
			expectedError: nil,
		},
		{
			name: "role binding polling timeout",
			input: kompose.ApplyKubernetesManifestsInput{
				Manifests:   []kompose.KubernetesManifest{deploymentManifest},
				Namespace:   "test-namespace",
				DryRun:      false,
				Force:       false,
				ComposeRepo: sampleRepo,
			},
			setupMocks: func(d *dependencies) {
				// No specific mocks needed
			},
			setupK8s: func(k8sClient *fake.Clientset) {
				// Create namespace but don't create RoleBinding to simulate timeout
				namespace := &corev1.Namespace{
					ObjectMeta: metav1.ObjectMeta{
						Name: "test-namespace",
					},
				}
				k8sClient.CoreV1().Namespaces().Create(context.Background(), namespace, metav1.CreateOptions{})
				// Don't create RoleBinding to simulate polling timeout
			},
			expectedResult: nil,
			expectedError:  errors.New("failed to poll for compose role binding"),
		},
		{
			name: "empty manifests list",
			input: kompose.ApplyKubernetesManifestsInput{
				Manifests:   []kompose.KubernetesManifest{},
				Namespace:   "test-namespace",
				DryRun:      false,
				Force:       false,
				ComposeRepo: sampleRepo,
			},
			setupMocks: func(d *dependencies) {
				// No specific mocks needed
			},
			setupK8s: func(k8sClient *fake.Clientset) {
				// Create namespace
				namespace := &corev1.Namespace{
					ObjectMeta: metav1.ObjectMeta{
						Name: "test-namespace",
					},
				}
				k8sClient.CoreV1().Namespaces().Create(context.Background(), namespace, metav1.CreateOptions{})

				// Create RoleBinding to simulate successful polling
				roleBinding := &rbacv1.RoleBinding{
					ObjectMeta: metav1.ObjectMeta{
						Name:      "compose-deployment-manager-role-binding",
						Namespace: "test-namespace",
					},
				}
				k8sClient.RbacV1().RoleBindings("test-namespace").Create(context.Background(), roleBinding, metav1.CreateOptions{})
			},
			expectedResult: &kompose.ApplyKubernetesManifestsResponse{
				AppliedResources: []kompose.AppliedResource{},
				Summary: kompose.ApplySummary{
					TotalResources:      0,
					SuccessfulResources: 0,
					FailedResources:     0,
					Actions:             []string{},
					ActionsCount:        map[string]int{},
				},
				Errors: []string{},
			},
			expectedError: nil,
		},
		{
			name: "apply with non-root security context",
			input: kompose.ApplyKubernetesManifestsInput{
				Manifests:   []kompose.KubernetesManifest{deploymentManifest},
				Namespace:   "test-namespace",
				DryRun:      false,
				Force:       false,
				ComposeRepo: sampleRepo,
				NonRoot: map[string]bool{
					"web": true,
				},
			},
			setupMocks: func(d *dependencies) {
				// No specific mocks needed
			},
			setupK8s: func(k8sClient *fake.Clientset) {
				// Create namespace
				namespace := &corev1.Namespace{
					ObjectMeta: metav1.ObjectMeta{
						Name: "test-namespace",
					},
				}
				k8sClient.CoreV1().Namespaces().Create(context.Background(), namespace, metav1.CreateOptions{})

				// Create RoleBinding to simulate successful polling
				roleBinding := &rbacv1.RoleBinding{
					ObjectMeta: metav1.ObjectMeta{
						Name:      "compose-deployment-manager-role-binding",
						Namespace: "test-namespace",
					},
				}
				k8sClient.RbacV1().RoleBindings("test-namespace").Create(context.Background(), roleBinding, metav1.CreateOptions{})
			},
			expectedResult: &kompose.ApplyKubernetesManifestsResponse{
				AppliedResources: []kompose.AppliedResource{
					{
						Kind:      "Deployment",
						Name:      "web",
						Namespace: "test-namespace",
						Action:    "created",
						Message:   "deployment.apps/web created",
					},
				},
				Summary: kompose.ApplySummary{
					TotalResources:      1,
					SuccessfulResources: 1,
					FailedResources:     0,
					Actions:             []string{"created"},
					ActionsCount:        map[string]int{"created": 1},
				},
				Errors: []string{},
			},
			expectedError: nil,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Setup mocks
			mockRepo := repoMocks.NewMockRepository(t)
			mockGitlab := gitlabMocks.NewMockGitlabClient(t)
			fakeK8sClient := fake.NewSimpleClientset()
			config := &configs.GlobalConfig{
				Space: &configs.SpaceConfig{
					SpaceDomain: "example.com",
				},
			}

			dep := dependencies{
				repo:      mockRepo,
				gitlab:    mockGitlab,
				k8sClient: fakeK8sClient,
				config:    config,
			}

			tt.setupMocks(&dep)
			if tt.setupK8s != nil {
				tt.setupK8s(fakeK8sClient)
			}

			// Create usecase instance
			usecase := kompose.New(dep.config, dep.repo, dep.k8sClient, dep.gitlab)

			// Execute test
			result, err := usecase.ApplyKubernetesManifests(context.Background(), tt.input)

			// Assertions
			if tt.expectedError != nil {
				assert.Error(t, err)
				assert.Contains(t, err.Error(), tt.expectedError.Error())
				assert.Nil(t, result)
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, result)

				// Verify summary
				assert.Equal(t, tt.expectedResult.Summary.TotalResources, result.Summary.TotalResources)
				assert.Equal(t, tt.expectedResult.Summary.SuccessfulResources, result.Summary.SuccessfulResources)
				assert.Equal(t, tt.expectedResult.Summary.FailedResources, result.Summary.FailedResources)

				// Verify applied resources count
				assert.Len(t, result.AppliedResources, len(tt.expectedResult.AppliedResources))

				// Verify errors
				assert.Len(t, result.Errors, len(tt.expectedResult.Errors))
			}

			// Verify all mocks were called as expected
			mockRepo.AssertExpectations(t)
			mockGitlab.AssertExpectations(t)
		})
	}
}

func TestParseManifestsFromFile(t *testing.T) {
	tests := []struct {
		name          string
		fileContent   string
		expectedCount int
		expectedError error
		expectedKinds []string
	}{
		{
			name: "single deployment manifest",
			fileContent: `apiVersion: apps/v1
kind: Deployment
metadata:
  name: web
spec:
  replicas: 1`,
			expectedCount: 1,
			expectedKinds: []string{"Deployment"},
			expectedError: nil,
		},
		{
			name: "multiple manifests separated by ---",
			fileContent: `apiVersion: apps/v1
kind: Deployment
metadata:
  name: web
spec:
  replicas: 1
---
apiVersion: v1
kind: Service
metadata:
  name: web-service
spec:
  selector:
    app: web
  ports:
  - port: 80`,
			expectedCount: 2,
			expectedKinds: []string{"Deployment", "Service"},
			expectedError: nil,
		},
		{
			name:          "empty file",
			fileContent:   ``,
			expectedCount: 0,
			expectedKinds: []string{},
			expectedError: errors.New("no valid Kubernetes resources found in file"),
		},
		{
			name: "invalid YAML",
			fileContent: `invalid: yaml: content:
  - this is not valid yaml
    missing proper indentation`,
			expectedCount: 0,
			expectedKinds: []string{},
			expectedError: errors.New("failed to parse YAML"),
		},
		{
			name: "manifest with empty document",
			fileContent: `apiVersion: apps/v1
kind: Deployment
metadata:
  name: web
spec:
  replicas: 1
---
---
apiVersion: v1
kind: Service
metadata:
  name: web-service
spec:
  selector:
    app: web`,
			expectedCount: 2,
			expectedKinds: []string{"Deployment", "Service"},
			expectedError: nil,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Create temporary file with test content
			tmpFile, err := os.CreateTemp("", "test-manifest-*.yaml")
			assert.NoError(t, err)
			defer os.Remove(tmpFile.Name())

			_, err = tmpFile.WriteString(tt.fileContent)
			assert.NoError(t, err)
			tmpFile.Close()

			// Execute test
			manifests, err := kompose.ParseManifestsFromFile(context.Background(), tmpFile.Name())

			// Assertions
			if tt.expectedError != nil {
				assert.Error(t, err)
				assert.Contains(t, err.Error(), tt.expectedError.Error())
				assert.Nil(t, manifests)
			} else {
				assert.NoError(t, err)
				assert.Len(t, manifests, tt.expectedCount)

				// Verify kinds
				for i, expectedKind := range tt.expectedKinds {
					if i < len(manifests) {
						assert.Equal(t, expectedKind, manifests[i].Kind)
						assert.NotEmpty(t, manifests[i].Name)
						assert.NotEmpty(t, manifests[i].Content)
					}
				}
			}
		})
	}
}

func TestDeleteKubernetesManifests(t *testing.T) {
	type dependencies struct {
		repo      *repoMocks.MockRepository
		gitlab    *gitlabMocks.MockGitlabClient
		config    *configs.GlobalConfig
		k8sClient *fake.Clientset
	}

	// Sample test data
	repoID := uuid.New()
	sampleRepo := &entities.Repository{
		BaseModel: entities.BaseModel{
			ID: repoID,
		},
		Name: "test-repo",
		Type: enums.RepoType_Composes,
	}

	tests := []struct {
		name           string
		input          kompose.DeleteKubernetesManifestsInput
		setupMocks     func(*dependencies)
		setupK8s       func(*fake.Clientset)
		expectedResult *kompose.DeleteKubernetesManifestsResponse
		expectedError  error
	}{
		{
			name: "successful delete all resources without namespace deletion",
			input: kompose.DeleteKubernetesManifestsInput{
				Namespace:       "test-namespace",
				DryRun:          false,
				DeleteNamespace: false,
				DeletePVC:       true,
				ComposeRepo:     sampleRepo,
			},
			setupMocks: func(d *dependencies) {
				// No specific mocks needed for this test
			},
			setupK8s: func(k8sClient *fake.Clientset) {
				// Create test resources with proper labels
				composeRepoLabel := fmt.Sprintf("compose-%s", repoID.String())

				// Create a deployment
				deployment := &appsv1.Deployment{
					ObjectMeta: metav1.ObjectMeta{
						Name:      "web",
						Namespace: "test-namespace",
						Labels: map[string]string{
							"compose-repo-id": composeRepoLabel,
							"managed-by":      "kompose-usecase",
						},
					},
				}
				k8sClient.AppsV1().Deployments("test-namespace").Create(context.Background(), deployment, metav1.CreateOptions{})

				// Create a service
				service := &corev1.Service{
					ObjectMeta: metav1.ObjectMeta{
						Name:      "web-service",
						Namespace: "test-namespace",
						Labels: map[string]string{
							"compose-repo-id": composeRepoLabel,
							"managed-by":      "kompose-usecase",
						},
					},
				}
				k8sClient.CoreV1().Services("test-namespace").Create(context.Background(), service, metav1.CreateOptions{})

				// Create a PVC
				pvc := &corev1.PersistentVolumeClaim{
					ObjectMeta: metav1.ObjectMeta{
						Name:      "data-pvc",
						Namespace: "test-namespace",
						Labels: map[string]string{
							"compose-repo-id": composeRepoLabel,
							"managed-by":      "kompose-usecase",
						},
					},
				}
				k8sClient.CoreV1().PersistentVolumeClaims("test-namespace").Create(context.Background(), pvc, metav1.CreateOptions{})

				// Create a ConfigMap
				configMap := &corev1.ConfigMap{
					ObjectMeta: metav1.ObjectMeta{
						Name:      "app-config",
						Namespace: "test-namespace",
						Labels: map[string]string{
							"compose-repo-id": composeRepoLabel,
							"managed-by":      "kompose-usecase",
						},
					},
				}
				k8sClient.CoreV1().ConfigMaps("test-namespace").Create(context.Background(), configMap, metav1.CreateOptions{})

				// Create a Secret
				secret := &corev1.Secret{
					ObjectMeta: metav1.ObjectMeta{
						Name:      "app-secret",
						Namespace: "test-namespace",
						Labels: map[string]string{
							"compose-repo-id": composeRepoLabel,
							"managed-by":      "kompose-usecase",
						},
					},
				}
				k8sClient.CoreV1().Secrets("test-namespace").Create(context.Background(), secret, metav1.CreateOptions{})
			},
			expectedResult: &kompose.DeleteKubernetesManifestsResponse{
				DeletedResources: []kompose.DeletedResource{
					{
						Kind:      "Deployment",
						Name:      "web",
						Namespace: "test-namespace",
						Message:   "deployment.apps/web deleted",
					},
					{
						Kind:      "Service",
						Name:      "web-service",
						Namespace: "test-namespace",
						Message:   "service/web-service deleted",
					},
					{
						Kind:      "ConfigMap",
						Name:      "app-config",
						Namespace: "test-namespace",
						Message:   "configmap/app-config deleted",
					},
					{
						Kind:      "PersistentVolumeClaim",
						Name:      "data-pvc",
						Namespace: "test-namespace",
						Message:   "persistentvolumeclaim/data-pvc deleted",
					},
					{
						Kind:      "Secret",
						Name:      "app-secret",
						Namespace: "test-namespace",
						Message:   "secret/app-secret deleted",
					},
				},
				Summary: kompose.DeleteSummary{
					TotalResources:      5,
					SuccessfulResources: 5,
					FailedResources:     0,
				},
				Errors: []string{},
			},
			expectedError: nil,
		},
		{
			name: "successful delete with namespace deletion",
			input: kompose.DeleteKubernetesManifestsInput{
				Namespace:       "test-namespace",
				DryRun:          false,
				DeleteNamespace: true,
				DeletePVC:       false,
				ComposeRepo:     sampleRepo,
			},
			setupMocks: func(d *dependencies) {
				// No specific mocks needed for this test
			},
			setupK8s: func(k8sClient *fake.Clientset) {
				// Create namespace
				namespace := &corev1.Namespace{
					ObjectMeta: metav1.ObjectMeta{
						Name: "test-namespace",
					},
				}
				k8sClient.CoreV1().Namespaces().Create(context.Background(), namespace, metav1.CreateOptions{})

				// Create test resources with proper labels
				composeRepoLabel := fmt.Sprintf("compose-%s", repoID.String())

				// Create a deployment
				deployment := &appsv1.Deployment{
					ObjectMeta: metav1.ObjectMeta{
						Name:      "web",
						Namespace: "test-namespace",
						Labels: map[string]string{
							"compose-repo-id": composeRepoLabel,
							"managed-by":      "kompose-usecase",
						},
					},
				}
				k8sClient.AppsV1().Deployments("test-namespace").Create(context.Background(), deployment, metav1.CreateOptions{})
			},
			expectedResult: &kompose.DeleteKubernetesManifestsResponse{
				DeletedResources: []kompose.DeletedResource{
					{
						Kind:      "Deployment",
						Name:      "web",
						Namespace: "test-namespace",
						Message:   "deployment.apps/web deleted",
					},
					{
						Kind:      "Namespace",
						Name:      "test-namespace",
						Namespace: "test-namespace",
						Message:   "namespace/test-namespace deleted",
					},
				},
				Summary: kompose.DeleteSummary{
					TotalResources:      2,
					SuccessfulResources: 2,
					FailedResources:     0,
				},
				Errors: []string{},
			},
			expectedError: nil,
		},
		{
			name: "dry run mode",
			input: kompose.DeleteKubernetesManifestsInput{
				Namespace:       "test-namespace",
				DryRun:          true,
				DeleteNamespace: false,
				DeletePVC:       true,
				ComposeRepo:     sampleRepo,
			},
			setupMocks: func(d *dependencies) {
				// No specific mocks needed for dry run
			},
			setupK8s: func(k8sClient *fake.Clientset) {
				// Create test resources with proper labels
				composeRepoLabel := fmt.Sprintf("compose-%s", repoID.String())

				// Create a deployment
				deployment := &appsv1.Deployment{
					ObjectMeta: metav1.ObjectMeta{
						Name:      "web",
						Namespace: "test-namespace",
						Labels: map[string]string{
							"compose-repo-id": composeRepoLabel,
							"managed-by":      "kompose-usecase",
						},
					},
				}
				k8sClient.AppsV1().Deployments("test-namespace").Create(context.Background(), deployment, metav1.CreateOptions{})

				// Create a service
				service := &corev1.Service{
					ObjectMeta: metav1.ObjectMeta{
						Name:      "web-service",
						Namespace: "test-namespace",
						Labels: map[string]string{
							"compose-repo-id": composeRepoLabel,
							"managed-by":      "kompose-usecase",
						},
					},
				}
				k8sClient.CoreV1().Services("test-namespace").Create(context.Background(), service, metav1.CreateOptions{})
			},
			expectedResult: &kompose.DeleteKubernetesManifestsResponse{
				DeletedResources: []kompose.DeletedResource{
					{
						Kind:      "Deployment",
						Name:      "web",
						Namespace: "test-namespace",
						Message:   "Deployment would be deleted (dry run)",
					},
					{
						Kind:      "Service",
						Name:      "web-service",
						Namespace: "test-namespace",
						Message:   "Service would be deleted (dry run)",
					},
				},
				Summary: kompose.DeleteSummary{
					TotalResources:      2,
					SuccessfulResources: 2,
					FailedResources:     0,
				},
				Errors: []string{},
			},
			expectedError: nil,
		},
		{
			name: "delete with no matching resources",
			input: kompose.DeleteKubernetesManifestsInput{
				Namespace:       "test-namespace",
				DryRun:          false,
				DeleteNamespace: false,
				DeletePVC:       false,
				ComposeRepo:     sampleRepo,
			},
			setupMocks: func(d *dependencies) {
				// No specific mocks needed
			},
			setupK8s: func(k8sClient *fake.Clientset) {
				// Don't create any resources - test empty namespace
			},
			expectedResult: &kompose.DeleteKubernetesManifestsResponse{
				DeletedResources: []kompose.DeletedResource{},
				Summary: kompose.DeleteSummary{
					TotalResources:      0,
					SuccessfulResources: 0,
					FailedResources:     0,
				},
				Errors: []string{},
			},
			expectedError: nil,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Setup mocks
			mockRepo := repoMocks.NewMockRepository(t)
			mockGitlab := gitlabMocks.NewMockGitlabClient(t)
			fakeK8sClient := fake.NewSimpleClientset()
			config := &configs.GlobalConfig{
				Space: &configs.SpaceConfig{
					SpaceDomain: "example.com",
				},
			}

			dep := dependencies{
				repo:      mockRepo,
				gitlab:    mockGitlab,
				k8sClient: fakeK8sClient,
				config:    config,
			}

			tt.setupMocks(&dep)
			if tt.setupK8s != nil {
				tt.setupK8s(fakeK8sClient)
			}

			// Create usecase instance
			usecase := kompose.New(dep.config, dep.repo, dep.k8sClient, dep.gitlab)

			// Execute test
			result, err := usecase.DeleteKubernetesManifests(context.Background(), tt.input)

			// Assertions
			if tt.expectedError != nil {
				assert.Error(t, err)
				assert.Contains(t, err.Error(), tt.expectedError.Error())
				assert.Nil(t, result)
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, result)

				// Verify summary
				assert.Equal(t, tt.expectedResult.Summary.TotalResources, result.Summary.TotalResources)
				assert.Equal(t, tt.expectedResult.Summary.SuccessfulResources, result.Summary.SuccessfulResources)
				assert.Equal(t, tt.expectedResult.Summary.FailedResources, result.Summary.FailedResources)

				// Verify deleted resources count
				assert.Len(t, result.DeletedResources, len(tt.expectedResult.DeletedResources))

				// Verify errors
				assert.Len(t, result.Errors, len(tt.expectedResult.Errors))

				// Verify specific deleted resources (if any)
				for i, expectedResource := range tt.expectedResult.DeletedResources {
					if i < len(result.DeletedResources) {
						assert.Equal(t, expectedResource.Kind, result.DeletedResources[i].Kind)
						assert.Equal(t, expectedResource.Name, result.DeletedResources[i].Name)
						assert.Equal(t, expectedResource.Namespace, result.DeletedResources[i].Namespace)
						// Note: Message might vary slightly, so we just check it's not empty for successful deletions
						if !tt.input.DryRun {
							assert.NotEmpty(t, result.DeletedResources[i].Message)
						}
					}
				}
			}

			// Verify all mocks were called as expected
			mockRepo.AssertExpectations(t)
			mockGitlab.AssertExpectations(t)
		})
	}
}

func TestDeleteAllResourceTypes(t *testing.T) {
	// Test individual delete functions for different resource types
	repoID := uuid.New()
	sampleRepo := &entities.Repository{
		BaseModel: entities.BaseModel{
			ID: repoID,
		},
		Name: "test-repo",
		Type: enums.RepoType_Composes,
	}

	input := kompose.DeleteKubernetesManifestsInput{
		Namespace:       "test-namespace",
		DryRun:          false,
		DeleteNamespace: false,
		DeletePVC:       true,
		ComposeRepo:     sampleRepo,
	}

	t.Run("deleteAllDeployments", func(t *testing.T) {
		fakeK8sClient := fake.NewSimpleClientset()
		config := &configs.GlobalConfig{}
		usecase := kompose.New(config, nil, fakeK8sClient, nil)

		// Create test deployment with proper labels
		composeRepoLabel := fmt.Sprintf("compose-%s", repoID.String())
		deployment := &appsv1.Deployment{
			ObjectMeta: metav1.ObjectMeta{
				Name:      "test-deployment",
				Namespace: "test-namespace",
				Labels: map[string]string{
					"compose-repo-id": composeRepoLabel,
					"managed-by":      "kompose-usecase",
				},
			},
		}
		fakeK8sClient.AppsV1().Deployments("test-namespace").Create(context.Background(), deployment, metav1.CreateOptions{})

		// Test the function through reflection or by calling the main function
		result, err := usecase.DeleteKubernetesManifests(context.Background(), input)

		assert.NoError(t, err)
		assert.NotNil(t, result)

		// Should have deleted the deployment
		found := false
		for _, resource := range result.DeletedResources {
			if resource.Kind == "Deployment" && resource.Name == "test-deployment" {
				found = true
				break
			}
		}
		assert.True(t, found, "Expected deployment to be deleted")
	})

	t.Run("deleteAllServices", func(t *testing.T) {
		fakeK8sClient := fake.NewSimpleClientset()
		config := &configs.GlobalConfig{}
		usecase := kompose.New(config, nil, fakeK8sClient, nil)

		// Create test service with proper labels
		composeRepoLabel := fmt.Sprintf("compose-%s", repoID.String())
		service := &corev1.Service{
			ObjectMeta: metav1.ObjectMeta{
				Name:      "test-service",
				Namespace: "test-namespace",
				Labels: map[string]string{
					"compose-repo-id": composeRepoLabel,
					"managed-by":      "kompose-usecase",
				},
			},
		}
		fakeK8sClient.CoreV1().Services("test-namespace").Create(context.Background(), service, metav1.CreateOptions{})

		// Test the function
		result, err := usecase.DeleteKubernetesManifests(context.Background(), input)

		assert.NoError(t, err)
		assert.NotNil(t, result)

		// Should have deleted the service
		found := false
		for _, resource := range result.DeletedResources {
			if resource.Kind == "Service" && resource.Name == "test-service" {
				found = true
				break
			}
		}
		assert.True(t, found, "Expected service to be deleted")
	})

	t.Run("deleteAllPVCs", func(t *testing.T) {
		fakeK8sClient := fake.NewSimpleClientset()
		config := &configs.GlobalConfig{}
		usecase := kompose.New(config, nil, fakeK8sClient, nil)

		// Create test PVC with proper labels
		composeRepoLabel := fmt.Sprintf("compose-%s", repoID.String())
		pvc := &corev1.PersistentVolumeClaim{
			ObjectMeta: metav1.ObjectMeta{
				Name:      "test-pvc",
				Namespace: "test-namespace",
				Labels: map[string]string{
					"compose-repo-id": composeRepoLabel,
					"managed-by":      "kompose-usecase",
				},
			},
		}
		fakeK8sClient.CoreV1().PersistentVolumeClaims("test-namespace").Create(context.Background(), pvc, metav1.CreateOptions{})

		// Test the function
		result, err := usecase.DeleteKubernetesManifests(context.Background(), input)

		assert.NoError(t, err)
		assert.NotNil(t, result)

		// Should have deleted the PVC
		found := false
		for _, resource := range result.DeletedResources {
			if resource.Kind == "PersistentVolumeClaim" && resource.Name == "test-pvc" {
				found = true
				break
			}
		}
		assert.True(t, found, "Expected PVC to be deleted")
	})

	t.Run("deleteAllConfigMaps", func(t *testing.T) {
		fakeK8sClient := fake.NewSimpleClientset()
		config := &configs.GlobalConfig{}
		usecase := kompose.New(config, nil, fakeK8sClient, nil)

		// Create test ConfigMap with proper labels
		composeRepoLabel := fmt.Sprintf("compose-%s", repoID.String())
		configMap := &corev1.ConfigMap{
			ObjectMeta: metav1.ObjectMeta{
				Name:      "test-configmap",
				Namespace: "test-namespace",
				Labels: map[string]string{
					"compose-repo-id": composeRepoLabel,
					"managed-by":      "kompose-usecase",
				},
			},
		}
		fakeK8sClient.CoreV1().ConfigMaps("test-namespace").Create(context.Background(), configMap, metav1.CreateOptions{})

		// Test the function
		result, err := usecase.DeleteKubernetesManifests(context.Background(), input)

		assert.NoError(t, err)
		assert.NotNil(t, result)

		// Should have deleted the ConfigMap
		found := false
		for _, resource := range result.DeletedResources {
			if resource.Kind == "ConfigMap" && resource.Name == "test-configmap" {
				found = true
				break
			}
		}
		assert.True(t, found, "Expected ConfigMap to be deleted")
	})

	t.Run("deleteAllSecrets", func(t *testing.T) {
		fakeK8sClient := fake.NewSimpleClientset()
		config := &configs.GlobalConfig{}
		usecase := kompose.New(config, nil, fakeK8sClient, nil)

		// Create test Secret with proper labels
		composeRepoLabel := fmt.Sprintf("compose-%s", repoID.String())
		secret := &corev1.Secret{
			ObjectMeta: metav1.ObjectMeta{
				Name:      "test-secret",
				Namespace: "test-namespace",
				Labels: map[string]string{
					"compose-repo-id": composeRepoLabel,
					"managed-by":      "kompose-usecase",
				},
			},
		}
		fakeK8sClient.CoreV1().Secrets("test-namespace").Create(context.Background(), secret, metav1.CreateOptions{})

		// Test the function
		result, err := usecase.DeleteKubernetesManifests(context.Background(), input)

		assert.NoError(t, err)
		assert.NotNil(t, result)

		// Should have deleted the Secret
		found := false
		for _, resource := range result.DeletedResources {
			if resource.Kind == "Secret" && resource.Name == "test-secret" {
				found = true
				break
			}
		}
		assert.True(t, found, "Expected Secret to be deleted")
	})
}

func TestDeleteKubernetesManifestsErrorScenarios(t *testing.T) {
	repoID := uuid.New()
	sampleRepo := &entities.Repository{
		BaseModel: entities.BaseModel{
			ID: repoID,
		},
		Name: "test-repo",
		Type: enums.RepoType_Composes,
	}

	t.Run("delete with resources already deleted", func(t *testing.T) {
		fakeK8sClient := fake.NewSimpleClientset()
		config := &configs.GlobalConfig{}
		usecase := kompose.New(config, nil, fakeK8sClient, nil)

		input := kompose.DeleteKubernetesManifestsInput{
			Namespace:       "test-namespace",
			DryRun:          false,
			DeleteNamespace: false,
			DeletePVC:       false,
			ComposeRepo:     sampleRepo,
		}

		// Don't create any resources, so they're "already deleted"
		result, err := usecase.DeleteKubernetesManifests(context.Background(), input)

		assert.NoError(t, err)
		assert.NotNil(t, result)
		assert.Equal(t, 0, result.Summary.TotalResources)
		assert.Equal(t, 0, result.Summary.SuccessfulResources)
		assert.Equal(t, 0, result.Summary.FailedResources)
		assert.Len(t, result.DeletedResources, 0)
		assert.Len(t, result.Errors, 0)
	})

	t.Run("delete namespace that doesn't exist", func(t *testing.T) {
		fakeK8sClient := fake.NewSimpleClientset()
		config := &configs.GlobalConfig{}
		usecase := kompose.New(config, nil, fakeK8sClient, nil)

		input := kompose.DeleteKubernetesManifestsInput{
			Namespace:       "non-existent-namespace",
			DryRun:          false,
			DeleteNamespace: true,
			DeletePVC:       false,
			ComposeRepo:     sampleRepo,
		}

		result, err := usecase.DeleteKubernetesManifests(context.Background(), input)

		assert.NoError(t, err)
		assert.NotNil(t, result)
		// Should handle non-existent namespace gracefully
		assert.Equal(t, 0, result.Summary.FailedResources)
	})

	t.Run("delete with PVC disabled", func(t *testing.T) {
		fakeK8sClient := fake.NewSimpleClientset()
		config := &configs.GlobalConfig{}
		usecase := kompose.New(config, nil, fakeK8sClient, nil)

		// Create test PVC with proper labels
		composeRepoLabel := fmt.Sprintf("compose-%s", repoID.String())
		pvc := &corev1.PersistentVolumeClaim{
			ObjectMeta: metav1.ObjectMeta{
				Name:      "test-pvc",
				Namespace: "test-namespace",
				Labels: map[string]string{
					"compose-repo-id": composeRepoLabel,
					"managed-by":      "kompose-usecase",
				},
			},
		}
		fakeK8sClient.CoreV1().PersistentVolumeClaims("test-namespace").Create(context.Background(), pvc, metav1.CreateOptions{})

		input := kompose.DeleteKubernetesManifestsInput{
			Namespace:       "test-namespace",
			DryRun:          false,
			DeleteNamespace: false,
			DeletePVC:       false, // PVC deletion disabled
			ComposeRepo:     sampleRepo,
		}

		result, err := usecase.DeleteKubernetesManifests(context.Background(), input)

		assert.NoError(t, err)
		assert.NotNil(t, result)

		// Should NOT have deleted the PVC
		found := false
		for _, resource := range result.DeletedResources {
			if resource.Kind == "PersistentVolumeClaim" && resource.Name == "test-pvc" {
				found = true
				break
			}
		}
		assert.False(t, found, "Expected PVC to NOT be deleted when DeletePVC is false")
	})
}
