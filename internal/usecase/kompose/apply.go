package kompose

import (
	"context"
	"fmt"
	"os"
	"strings"
	"time"

	"github.com/google/uuid"
	"go.opentelemetry.io/otel/attribute"
	"go.opentelemetry.io/otel/trace"
	"gopkg.in/yaml.v3"
	appsv1 "k8s.io/api/apps/v1"
	corev1 "k8s.io/api/core/v1"
	networkingv1 "k8s.io/api/networking/v1"
	"k8s.io/apimachinery/pkg/api/errors"
	"k8s.io/apimachinery/pkg/api/resource"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/apis/meta/v1/unstructured"
	"k8s.io/apimachinery/pkg/runtime"
	kubeYaml "k8s.io/apimachinery/pkg/util/yaml"
	"k8s.io/client-go/kubernetes"

	"api-server/internal/entities"
	"api-server/internal/enums"
	"api-server/internal/utils"
	"api-server/pkg/oteltrace"
)

const (
	// for polling Compose role binding
	COMPOSE_DEPLOYMENT_MANAGER_ROLE_BINDING = "compose-deployment-manager-role-binding"
	POLL_COMPOSE_ROLE_BINDING_INTERVAL      = 1 * time.Second
	POLL_COMPOSE_ROLE_BINDING_MAX_ATTEMPTS  = 10

	// these ID are used for security context, need to match uid/gid in EFS Storage Class
	POSIX_UID     = 1000
	POSIX_GID     = 1000
	POSIX_FSGROUP = 1000
)

var defaultMemory = utils.Ptr("512Mi") // default

type ApplyKubernetesManifestsInput struct {
	// Manifests contains the Kubernetes manifests to apply
	Manifests []KubernetesManifest `json:"manifests" validate:"required,min=1"`

	// Namespace is the target namespace for the resources (optional, uses manifest namespace if not specified)
	Namespace string `json:"namespace,omitempty" validate:"omitempty,min=1,max=63" example:"default"`

	// DryRun specifies whether to perform a dry run (validation only)
	DryRun bool `json:"dry_run,omitempty" example:"false"`

	// Force specifies whether to force apply (delete and recreate if necessary)
	Force bool `json:"force,omitempty" example:"false"`

	ComposeRepo *entities.Repository

	// ResourceRequirements contains resource requests and limits for each service
	ResourceRequirements map[string]*ResourceRequirement

	// GPURequirements contains GPU requirements for each service
	GPURequirements map[string]*GPURequirement

	NonRoot map[string]bool
}

// AppliedResource represents a successfully applied Kubernetes resource
type AppliedResource struct {
	// Kind is the Kubernetes resource kind
	Kind string `json:"kind" example:"Deployment"`

	// Name is the name of the resource
	Name string `json:"name" example:"web"`

	// Namespace is the namespace where the resource was applied
	Namespace string `json:"namespace" example:"default"`

	// Action describes what action was taken (created, updated, unchanged)
	Action string `json:"action" example:"created"`

	// Message provides additional information about the operation
	Message string `json:"message,omitempty" example:"deployment.apps/web created"`
}

// ApplyKubernetesManifestsResponse represents the response for applying Kubernetes manifests
type ApplyKubernetesManifestsResponse struct {
	// AppliedResources contains information about successfully applied resources
	AppliedResources []AppliedResource `json:"applied_resources"`

	// Summary provides a summary of the apply operation
	Summary ApplySummary `json:"summary"`

	// Errors contains any errors that occurred during the apply operation
	Errors []string `json:"errors,omitempty"`
}

// ApplySummary provides statistics about the apply operation
type ApplySummary struct {
	// TotalResources is the total number of resources processed
	TotalResources int `json:"total_resources" example:"5"`

	// SuccessfulResources is the number of resources successfully applied
	SuccessfulResources int `json:"successful_resources" example:"4"`

	// FailedResources is the number of resources that failed to apply
	FailedResources int `json:"failed_resources" example:"1"`

	// Actions is a count of each action type performed
	ActionsCount map[string]int `json:"actions_count" example:"{\"created\":3,\"updated\":1,\"unchanged\":1}"`

	Actions []string `json:"actions" example:"[\"created\",\"updated\",\"unchanged\"]"`
}

// ParseManifestsFromFile reads a Kubernetes manifest file that may contain multiple resources
// and parses the content to a slice of KubernetesManifest structs
func ParseManifestsFromFile(ctx context.Context, filePath string) ([]KubernetesManifest, error) {
	content, err := os.ReadFile(filePath)
	if err != nil {
		return nil, fmt.Errorf("failed to read manifest file: %w", err)
	}

	var manifests []KubernetesManifest

	// Split the YAML content by document separator and process each document
	decoder := kubeYaml.NewYAMLOrJSONDecoder(strings.NewReader(string(content)), 4096)
	for {
		// Create a new unstructured object for each document
		obj := &unstructured.Unstructured{}
		if err := decoder.Decode(obj); err != nil {
			// Break when we reach the end of the input
			if err.Error() == "EOF" {
				break
			}
			return nil, fmt.Errorf("failed to parse YAML: %w", err)
		}

		// Skip empty documents
		if obj.GetKind() == "" {
			continue
		}

		// Extract the specific document content
		// We need to re-marshal the object to get its YAML representation
		docContent, err := yaml.Marshal(obj)
		if err != nil {
			return nil, fmt.Errorf("failed to marshal resource: %w", err)
		}

		// Create KubernetesManifest from the parsed content
		manifest := KubernetesManifest{
			Kind:    obj.GetKind(),
			Name:    obj.GetName(),
			Content: string(docContent),
		}

		manifests = append(manifests, manifest)
	}

	if len(manifests) == 0 {
		return nil, fmt.Errorf("no valid Kubernetes resources found in file")
	}

	return manifests, nil
}

// ApplyKubernetesManifests applies the generated Kubernetes manifests to a cluster
func (u *impl) ApplyKubernetesManifests(ctx context.Context, input ApplyKubernetesManifestsInput) (*ApplyKubernetesManifestsResponse, error) {
	// create a new namespace
	if input.Namespace != "" {
		_, err := u.k8sClient.CoreV1().Namespaces().Get(ctx, input.Namespace, metav1.GetOptions{})
		if err != nil {
			if errors.IsNotFound(err) {
				_, err := u.k8sClient.CoreV1().Namespaces().Create(ctx, &corev1.Namespace{
					ObjectMeta: metav1.ObjectMeta{
						Name: input.Namespace,
						Labels: map[string]string{
							"app":             input.Namespace,
							"managed-by":      "kompose-usecase", // Add managed-by label
							"compose-repo-id": composeRepoName(input.ComposeRepo.ID.String()),
						},
						Annotations: map[string]string{
							"kompose.cmd": "kompose",
						},
					},
				}, metav1.CreateOptions{})
				if err != nil {
					return nil, fmt.Errorf("failed to create namespace: %w", err)
				}
			} else {
				return nil, fmt.Errorf("failed to get namespace: %w", err)
			}
		}
	}

	// poll for RoleBinding to be created
	err := pollComposeRoleBinding(ctx, u.k8sClient, input.Namespace)
	if err != nil {
		return nil, fmt.Errorf("failed to poll for compose role binding: %w", err)
	}

	var appliedResources []AppliedResource
	var errors []string
	var actions []string
	actionCounts := make(map[string]int)

	// Process each manifest
	for _, manifest := range input.Manifests {
		appliedResource, err := u.applyManifest(ctx, manifest, input)
		if err != nil {
			errors = append(errors, fmt.Sprintf("Failed to apply %s/%s: %v", manifest.Kind, manifest.Name, err))
			continue
		}

		if appliedResource != nil {
			appliedResources = append(appliedResources, *appliedResource)
			actions = append(actions, appliedResource.Action)
			actionCounts[appliedResource.Action]++
		}
	}

	// Calculate summary
	summary := ApplySummary{
		TotalResources:      len(input.Manifests),
		SuccessfulResources: len(appliedResources),
		FailedResources:     len(errors),
		Actions:             actions,
		ActionsCount:        actionCounts,
	}

	response := &ApplyKubernetesManifestsResponse{
		AppliedResources: appliedResources,
		Summary:          summary,
		Errors:           errors,
	}

	return response, nil
}

// applyManifest applies a single Kubernetes manifest
func (u *impl) applyManifest(ctx context.Context, manifest KubernetesManifest, input ApplyKubernetesManifestsInput) (*AppliedResource, error) {
	// Parse the YAML content
	obj := &unstructured.Unstructured{}
	if err := yaml.Unmarshal([]byte(manifest.Content), obj); err != nil {
		return nil, fmt.Errorf("failed to parse YAML: %w", err)
	}

	// Set namespace if provided in input and not already set
	if input.Namespace != "" && obj.GetNamespace() == "" {
		obj.SetNamespace(input.Namespace)
	}

	// Add tracking labels to identify resources managed by this tool
	labels := obj.GetLabels()
	if labels == nil {
		labels = make(map[string]string)
	}
	labels["managed-by"] = "kompose-usecase"
	labels["kompose-app"] = "true"
	labels["compose-repo-id"] = composeRepoName(input.ComposeRepo.ID.String())
	labels["app"] = utils.Slugify(obj.GetName())
	obj.SetLabels(labels)

	// Add timestamp annotation to track when the resource was applied
	annotations := obj.GetAnnotations()
	if annotations == nil {
		annotations = make(map[string]string)
	}
	annotations["kompose-applied-at"] = time.Now().Format(time.RFC3339)
	annotations["timestamp"] = time.Now().Format(time.RFC3339)
	obj.SetAnnotations(annotations)

	// Handle different resource types
	switch strings.ToLower(manifest.Kind) {
	case "deployment":
		return u.applyDeployment(ctx, obj, input)
	case "service":
		return u.applyService(ctx, obj, input)
	case "persistentvolumeclaim", "pvc":
		return u.applyPVC(ctx, obj, input)
	case "configmap":
		return u.applyConfigMap(ctx, obj, input)
	case "secret":
		return u.applySecret(ctx, obj, input)
	case "ingress":
		return u.applyIngress(ctx, obj, input)
	default:
		return u.applyGenericResource(ctx, obj, input)
	}
}

// applyDeployment applies a Deployment resource
func (u *impl) applyDeployment(ctx context.Context, obj *unstructured.Unstructured, input ApplyKubernetesManifestsInput) (*AppliedResource, error) {
	deploymentClient := u.k8sClient.AppsV1().Deployments(obj.GetNamespace())

	if input.DryRun {
		return &AppliedResource{
			Kind:      "Deployment",
			Name:      obj.GetName(),
			Namespace: obj.GetNamespace(),
			Action:    "dry-run",
			Message:   "Deployment would be created/updated (dry run)",
		}, nil
	}

	// Convert unstructured to typed Deployment
	deployment := &appsv1.Deployment{}
	if err := runtime.DefaultUnstructuredConverter.FromUnstructured(obj.Object, deployment); err != nil {
		return nil, fmt.Errorf("failed to convert to Deployment: %w", err)
	}

	// add new label for pod in this deployment
	podLabels := deployment.Spec.Template.GetLabels()
	if podLabels == nil {
		podLabels = make(map[string]string)
	}
	podLabels["compose-repo-id"] = composeRepoName(input.ComposeRepo.ID.String())
	app := utils.Slugify(obj.GetName())
	podLabels["app"] = app
	deployment.Spec.Template.SetLabels(podLabels)
	applyAppSelector(deployment, app)

	// Apply resource requirements
	if err := applyResourceRequirements(deployment, input.ResourceRequirements); err != nil {
		return nil, fmt.Errorf("failed to apply resource requirements: %w", err)
	}

	// Apply GPU requirements
	if err := applyGPURequirements(deployment, input.GPURequirements); err != nil {
		return nil, fmt.Errorf("failed to apply GPU requirements: %w", err)
	}

	// Apply security context at Pod-level
	if input.NonRoot != nil {
		if value, exists := input.NonRoot[deployment.Name]; exists && value {
			deployment.Spec.Template.Spec.SecurityContext = &corev1.PodSecurityContext{
				FSGroup:    utils.Ptr[int64](POSIX_FSGROUP),
				RunAsUser:  utils.Ptr[int64](POSIX_UID),
				RunAsGroup: utils.Ptr[int64](POSIX_GID),
			}
		}
	}

	// Try to get existing deployment
	existing, err := deploymentClient.Get(ctx, deployment.Name, metav1.GetOptions{})
	if err != nil {
		if errors.IsNotFound(err) {
			// Create new deployment
			_, err := deploymentClient.Create(ctx, deployment, metav1.CreateOptions{})
			if err != nil {
				return nil, fmt.Errorf("failed to create deployment: %w", err)
			}
			return &AppliedResource{
				Kind:      "Deployment",
				Name:      deployment.Name,
				Namespace: deployment.Namespace,
				Action:    "created",
				Message:   fmt.Sprintf("deployment.apps/%s created", deployment.Name),
			}, nil
		}
		return nil, fmt.Errorf("failed to get deployment: %w", err)
	}

	// Update existing deployment
	deployment.ResourceVersion = existing.ResourceVersion
	_, err = deploymentClient.Update(ctx, deployment, metav1.UpdateOptions{})
	if err != nil {
		return nil, fmt.Errorf("failed to update deployment: %w", err)
	}

	return &AppliedResource{
		Kind:      "Deployment",
		Name:      deployment.Name,
		Namespace: deployment.Namespace,
		Action:    "updated",
		Message:   fmt.Sprintf("deployment.apps/%s updated", deployment.Name),
	}, nil
}

// applyService applies a Service resource
func (u *impl) applyService(ctx context.Context, obj *unstructured.Unstructured, input ApplyKubernetesManifestsInput) (*AppliedResource, error) {
	serviceClient := u.k8sClient.CoreV1().Services(obj.GetNamespace())

	if input.DryRun {
		return &AppliedResource{
			Kind:      "Service",
			Name:      obj.GetName(),
			Namespace: obj.GetNamespace(),
			Action:    "dry-run",
			Message:   "Service would be created/updated (dry run)",
		}, nil
	}

	// Convert unstructured to typed Service
	service := &corev1.Service{}
	if err := runtime.DefaultUnstructuredConverter.FromUnstructured(obj.Object, service); err != nil {
		return nil, fmt.Errorf("failed to convert to Service: %w", err)
	}

	serviceName := utils.Slugify(service.Name)

	// add new label
	labels := service.GetLabels()
	if labels == nil {
		labels = make(map[string]string)
	}
	labels["app"] = serviceName
	service.SetLabels(labels)

	service.Name = serviceName

	// Try to get existing service
	existing, err := serviceClient.Get(ctx, service.Name, metav1.GetOptions{})
	if err != nil {
		if errors.IsNotFound(err) {
			// Create new service
			_, err := serviceClient.Create(ctx, service, metav1.CreateOptions{})
			if err != nil {
				return nil, fmt.Errorf("failed to create service: %w", err)
			}
			return &AppliedResource{
				Kind:      "Service",
				Name:      service.Name,
				Namespace: service.Namespace,
				Action:    "created",
				Message:   fmt.Sprintf("service/%s created", service.Name),
			}, nil
		}
		return nil, fmt.Errorf("failed to get service: %w", err)
	}

	// Update existing service
	service.ResourceVersion = existing.ResourceVersion
	service.Spec.ClusterIP = existing.Spec.ClusterIP // Preserve cluster IP
	_, err = serviceClient.Update(ctx, service, metav1.UpdateOptions{})
	if err != nil {
		return nil, fmt.Errorf("failed to update service: %w", err)
	}

	return &AppliedResource{
		Kind:      "Service",
		Name:      service.Name,
		Namespace: service.Namespace,
		Action:    "updated",
		Message:   fmt.Sprintf("service/%s updated", service.Name),
	}, nil
}

// applyPVC applies a PersistentVolumeClaim resource
func (u *impl) applyPVC(ctx context.Context, obj *unstructured.Unstructured, input ApplyKubernetesManifestsInput) (*AppliedResource, error) {
	pvcClient := u.k8sClient.CoreV1().PersistentVolumeClaims(obj.GetNamespace())

	if input.DryRun {
		return &AppliedResource{
			Kind:      "PersistentVolumeClaim",
			Name:      obj.GetName(),
			Namespace: obj.GetNamespace(),
			Action:    "dry-run",
			Message:   "PersistentVolumeClaim would be created/updated (dry run)",
		}, nil
	}

	// Convert unstructured to typed PVC
	pvc := &corev1.PersistentVolumeClaim{}
	if err := runtime.DefaultUnstructuredConverter.FromUnstructured(obj.Object, pvc); err != nil {
		return nil, fmt.Errorf("failed to convert to PersistentVolumeClaim: %w", err)
	}

	// update annotations
	if pvc.Annotations == nil {
		pvc.Annotations = make(map[string]string)
	}

	// change access modes to ReadWriteMany because we're using EFS
	pvc.Spec.AccessModes = []corev1.PersistentVolumeAccessMode{corev1.ReadWriteMany}
	// Filesystem because of EFS
	pvc.Spec.VolumeMode = utils.Ptr(corev1.PersistentVolumeFilesystem)
	// check if storage class is defined in spec or empty, if not, add it
	if pvc.Spec.StorageClassName == nil || *pvc.Spec.StorageClassName == "" {
		pvc.Spec.StorageClassName = utils.Ptr(VOLVO_EFS_STORAGE_CLASS_NAME)
	}

	// Try to get existing PVC
	_, err := pvcClient.Get(ctx, pvc.Name, metav1.GetOptions{})
	if err != nil {
		if errors.IsNotFound(err) {
			// Create new PVC
			_, err := pvcClient.Create(ctx, pvc, metav1.CreateOptions{})
			if err != nil {
				return nil, fmt.Errorf("failed to create PVC: %w", err)
			}
			return &AppliedResource{
				Kind:      "PersistentVolumeClaim",
				Name:      pvc.Name,
				Namespace: pvc.Namespace,
				Action:    "created",
				Message:   fmt.Sprintf("persistentvolumeclaim/%s created", pvc.Name),
			}, nil
		}
		return nil, fmt.Errorf("failed to get PVC: %w", err)
	}

	// PVCs are generally immutable after creation, so we just report as unchanged
	return &AppliedResource{
		Kind:      "PersistentVolumeClaim",
		Name:      pvc.Name,
		Namespace: pvc.Namespace,
		Action:    "unchanged",
		Message:   fmt.Sprintf("persistentvolumeclaim/%s unchanged", pvc.Name),
	}, nil
}

// applyConfigMap applies a ConfigMap resource
func (u *impl) applyConfigMap(ctx context.Context, obj *unstructured.Unstructured, input ApplyKubernetesManifestsInput) (*AppliedResource, error) {
	configMapClient := u.k8sClient.CoreV1().ConfigMaps(obj.GetNamespace())

	if input.DryRun {
		return &AppliedResource{
			Kind:      "ConfigMap",
			Name:      obj.GetName(),
			Namespace: obj.GetNamespace(),
			Action:    "dry-run",
			Message:   "ConfigMap would be created/updated (dry run)",
		}, nil
	}

	// Convert unstructured to typed ConfigMap
	configMap := &corev1.ConfigMap{}
	if err := runtime.DefaultUnstructuredConverter.FromUnstructured(obj.Object, configMap); err != nil {
		return nil, fmt.Errorf("failed to convert to ConfigMap: %w", err)
	}

	// Try to get existing ConfigMap
	existing, err := configMapClient.Get(ctx, configMap.Name, metav1.GetOptions{})
	if err != nil {
		if errors.IsNotFound(err) {
			// Create new ConfigMap
			_, err := configMapClient.Create(ctx, configMap, metav1.CreateOptions{})
			if err != nil {
				return nil, fmt.Errorf("failed to create ConfigMap: %w", err)
			}
			return &AppliedResource{
				Kind:      "ConfigMap",
				Name:      configMap.Name,
				Namespace: configMap.Namespace,
				Action:    "created",
				Message:   fmt.Sprintf("configmap/%s created", configMap.Name),
			}, nil
		}
		return nil, fmt.Errorf("failed to get ConfigMap: %w", err)
	}

	// Update existing ConfigMap
	configMap.ResourceVersion = existing.ResourceVersion
	_, err = configMapClient.Update(ctx, configMap, metav1.UpdateOptions{})
	if err != nil {
		return nil, fmt.Errorf("failed to update ConfigMap: %w", err)
	}

	return &AppliedResource{
		Kind:      "ConfigMap",
		Name:      configMap.Name,
		Namespace: configMap.Namespace,
		Action:    "updated",
		Message:   fmt.Sprintf("configmap/%s updated", configMap.Name),
	}, nil
}

// applySecret applies a Secret resource
func (u *impl) applySecret(ctx context.Context, obj *unstructured.Unstructured, input ApplyKubernetesManifestsInput) (*AppliedResource, error) {
	secretClient := u.k8sClient.CoreV1().Secrets(obj.GetNamespace())

	if input.DryRun {
		return &AppliedResource{
			Kind:      "Secret",
			Name:      obj.GetName(),
			Namespace: obj.GetNamespace(),
			Action:    "dry-run",
			Message:   "Secret would be created/updated (dry run)",
		}, nil
	}

	// Convert unstructured to typed Secret
	secret := &corev1.Secret{}
	if err := runtime.DefaultUnstructuredConverter.FromUnstructured(obj.Object, secret); err != nil {
		return nil, fmt.Errorf("failed to convert to Secret: %w", err)
	}

	// Try to get existing Secret
	existing, err := secretClient.Get(ctx, secret.Name, metav1.GetOptions{})
	if err != nil {
		if errors.IsNotFound(err) {
			// Create new Secret
			_, err := secretClient.Create(ctx, secret, metav1.CreateOptions{})
			if err != nil {
				return nil, fmt.Errorf("failed to create Secret: %w", err)
			}
			return &AppliedResource{
				Kind:      "Secret",
				Name:      secret.Name,
				Namespace: secret.Namespace,
				Action:    "created",
				Message:   fmt.Sprintf("secret/%s created", secret.Name),
			}, nil
		}
		return nil, fmt.Errorf("failed to get Secret: %w", err)
	}

	// Update existing Secret
	secret.ResourceVersion = existing.ResourceVersion
	_, err = secretClient.Update(ctx, secret, metav1.UpdateOptions{})
	if err != nil {
		return nil, fmt.Errorf("failed to update Secret: %w", err)
	}

	return &AppliedResource{
		Kind:      "Secret",
		Name:      secret.Name,
		Namespace: secret.Namespace,
		Action:    "updated",
		Message:   fmt.Sprintf("secret/%s updated", secret.Name),
	}, nil
}

// applyIngress applies an Ingress resource
func (u *impl) applyIngress(ctx context.Context, obj *unstructured.Unstructured, input ApplyKubernetesManifestsInput) (*AppliedResource, error) {
	ingressClient := u.k8sClient.NetworkingV1().Ingresses(obj.GetNamespace())

	if input.DryRun {
		return &AppliedResource{
			Kind:      "Ingress",
			Name:      obj.GetName(),
			Namespace: obj.GetNamespace(),
			Action:    "dry-run",
			Message:   "Ingress would be created/updated (dry run)",
		}, nil
	}

	// Convert unstructured to typed Ingress
	ingress := &networkingv1.Ingress{}
	if err := runtime.DefaultUnstructuredConverter.FromUnstructured(obj.Object, ingress); err != nil {
		return nil, fmt.Errorf("failed to convert to Ingress: %w", err)
	}

	resourceName := ComposeResourceName(ComposeDeploymentName(input.ComposeRepo.Name, ingress.Name))

	// update host for current rule of Ingress
	for i, _ := range ingress.Spec.Rules {
		url := CreateComposeDeploymentURL(resourceName, input.ComposeRepo.ID, u.config.Space.SpaceDomain)
		ingress.Spec.Rules[i].Host = url
	}

	// update proxy-body-size with default 100m if nginx.ingress.kubernetes.io/proxy-body-size not exist g
	if ingress.Annotations == nil {
		ingress.Annotations = make(map[string]string)
	}
	if _, ok := ingress.Annotations["nginx.ingress.kubernetes.io/proxy-body-size"]; !ok {
		ingress.Annotations["nginx.ingress.kubernetes.io/proxy-body-size"] = "100m" // default value
	}

	// Try to get existing Ingress
	existing, err := ingressClient.Get(ctx, ingress.Name, metav1.GetOptions{})
	if err != nil {
		if errors.IsNotFound(err) {
			// Create new Ingress
			_, err := ingressClient.Create(ctx, ingress, metav1.CreateOptions{})
			if err != nil {
				return nil, fmt.Errorf("failed to create Ingress: %w", err)
			}
			return &AppliedResource{
				Kind:      "Ingress",
				Name:      ingress.Name,
				Namespace: ingress.Namespace,
				Action:    "created",
				Message:   fmt.Sprintf("ingress/%s created", ingress.Name),
			}, nil
		}
		return nil, fmt.Errorf("failed to get Ingress: %w", err)
	}

	// Update existing Ingress
	ingress.ResourceVersion = existing.ResourceVersion
	_, err = ingressClient.Update(ctx, ingress, metav1.UpdateOptions{})
	if err != nil {
		return nil, fmt.Errorf("failed to update Ingress: %w", err)
	}

	return &AppliedResource{
		Kind:      "Ingress",
		Name:      ingress.Name,
		Namespace: ingress.Namespace,
		Action:    "updated",
		Message:   fmt.Sprintf("ingress/%s updated", ingress.Name),
	}, nil
}

func CreateComposeDeploymentURL(composeResourceName string, composeRepoID uuid.UUID, domain string) string {
	url := utils.GetDeploymentURL(composeResourceName, domain)
	if len(url) > 63 {
		url = utils.GetDeploymentURL(ComposeResourceName(composeRepoID.String()), domain)
	}
	return url
}

// applyGenericResource applies a generic Kubernetes resource using dynamic client
func (u *impl) applyGenericResource(ctx context.Context, obj *unstructured.Unstructured, input ApplyKubernetesManifestsInput) (*AppliedResource, error) {
	// For now, return a placeholder implementation
	// In a real implementation, we would use the dynamic client to apply the resource

	if input.DryRun {
		return &AppliedResource{
			Kind:      obj.GetKind(),
			Name:      obj.GetName(),
			Namespace: obj.GetNamespace(),
			Action:    "dry-run",
			Message:   fmt.Sprintf("%s would be created/updated (dry run)", obj.GetKind()),
		}, nil
	}

	// Placeholder for generic resource application
	return &AppliedResource{
		Kind:      obj.GetKind(),
		Name:      obj.GetName(),
		Namespace: obj.GetNamespace(),
		Action:    "created",
		Message:   fmt.Sprintf("%s/%s created (placeholder)", strings.ToLower(obj.GetKind()), obj.GetName()),
	}, nil
}

// DeleteKubernetesManifestsInput represents the input for deleting Kubernetes manifests
type DeleteKubernetesManifestsInput struct {
	// // Manifests contains the Kubernetes manifests to delete (optional)
	// Manifests []KubernetesManifest `json:"manifests,omitempty"`

	// Namespace is the target namespace for the resources
	Namespace string `json:"namespace" validate:"required,min=1,max=63" example:"default"`

	// DryRun specifies whether to perform a dry run (validation only)
	DryRun bool `json:"dry_run,omitempty" example:"false"`

	// DeleteNamespace specifies whether to delete the namespace itself
	DeleteNamespace bool

	// DeletePVC specifies whether to delete PVCs
	DeletePVC bool

	ComposeRepo *entities.Repository
}

// DeleteKubernetesManifestsResponse represents the response for deleting Kubernetes manifests
type DeleteKubernetesManifestsResponse struct {
	// DeletedResources contains information about successfully deleted resources
	DeletedResources []DeletedResource `json:"deleted_resources"`

	// Summary provides a summary of the delete operation
	Summary DeleteSummary `json:"summary"`

	// Errors contains any errors that occurred during the delete operation
	Errors []string `json:"errors,omitempty"`
}

// DeletedResource represents a successfully deleted Kubernetes resource
type DeletedResource struct {
	// Kind is the resource kind (e.g., Deployment, Service)
	Kind string `json:"kind"`

	// Name is the resource name
	Name string `json:"name"`

	// Namespace is the resource namespace
	Namespace string `json:"namespace"`

	// Message contains additional information about the deletion
	Message string `json:"message,omitempty"`
}

// DeleteSummary provides a summary of the delete operation
type DeleteSummary struct {
	// TotalResources is the total number of resources that were attempted to be deleted
	TotalResources int `json:"total_resources"`

	// SuccessfulResources is the number of resources that were successfully deleted
	SuccessfulResources int `json:"successful_resources"`

	// FailedResources is the number of resources that failed to be deleted
	FailedResources int `json:"failed_resources"`
}

// DeleteKubernetesManifests deletes the Kubernetes resources that were previously applied
func (u *impl) DeleteKubernetesManifests(ctx context.Context, input DeleteKubernetesManifestsInput) (*DeleteKubernetesManifestsResponse, error) {
	var deletedResources []DeletedResource
	var errors []string

	// Delete all resources with our tracking label in the specified namespace
	deployments, err := u.deleteAllDeployments(ctx, input)
	if err != nil {
		errors = append(errors, fmt.Sprintf("Failed to delete deployments: %v", err))
	} else {
		deletedResources = append(deletedResources, deployments...)
	}

	services, err := u.deleteAllServices(ctx, input)
	if err != nil {
		errors = append(errors, fmt.Sprintf("Failed to delete services: %v", err))
	} else {
		deletedResources = append(deletedResources, services...)
	}

	ingresses, err := u.deleteAllIngresses(ctx, input)
	if err != nil {
		errors = append(errors, fmt.Sprintf("Failed to delete ingresses: %v", err))
	} else {
		deletedResources = append(deletedResources, ingresses...)
	}

	configmaps, err := u.deleteAllConfigMaps(ctx, input)
	if err != nil {
		errors = append(errors, fmt.Sprintf("Failed to delete configmaps: %v", err))
	} else {
		deletedResources = append(deletedResources, configmaps...)
	}

	if input.DeletePVC {
		pvcs, err := u.deleteAllPVCs(ctx, input)
		if err != nil {
			errors = append(errors, fmt.Sprintf("Failed to delete pvcs: %v", err))
		} else {
			deletedResources = append(deletedResources, pvcs...)
		}
	}

	secrets, err := u.deleteAllSecrets(ctx, input)
	if err != nil {
		errors = append(errors, fmt.Sprintf("Failed to delete secrets: %v", err))
	} else {
		deletedResources = append(deletedResources, secrets...)
	}

	if input.DeleteNamespace {
		// delete namespace at the end
		namespace, err := u.deleteNamespace(ctx, input)
		if err != nil {
			errors = append(errors, fmt.Sprintf("Failed to delete namespace: %v", err))
		} else {
			deletedResources = append(deletedResources, *namespace)
		}
	}

	// Calculate summary
	// totalResources := len(input.Manifests)
	totalResources := len(deletedResources) + len(errors)

	summary := DeleteSummary{
		TotalResources:      totalResources,
		SuccessfulResources: len(deletedResources),
		FailedResources:     len(errors),
	}

	response := &DeleteKubernetesManifestsResponse{
		DeletedResources: deletedResources,
		Summary:          summary,
		Errors:           errors,
	}

	return response, nil
}

// deleteManifest deletes a single Kubernetes manifest
func (u *impl) deleteManifest(ctx context.Context, manifest KubernetesManifest, input DeleteKubernetesManifestsInput) (*DeletedResource, error) {
	// Parse the YAML content
	obj := &unstructured.Unstructured{}
	if err := yaml.Unmarshal([]byte(manifest.Content), obj); err != nil {
		return nil, fmt.Errorf("failed to parse YAML: %w", err)
	}

	// Set namespace if provided in input and not already set
	namespace := obj.GetNamespace()
	if input.Namespace != "" {
		namespace = input.Namespace
	}
	if namespace == "" {
		namespace = "default"
	}

	// Create delete options
	deleteOptions := metav1.DeleteOptions{}
	if input.DryRun {
		deleteOptions.DryRun = []string{"All"}
	}

	// Handle different resource types
	switch strings.ToLower(manifest.Kind) {
	case "deployment":
		return u.deleteDeployment(ctx, obj, namespace, deleteOptions)
	case "service":
		return u.deleteService(ctx, obj, namespace, deleteOptions)
	case "persistentvolumeclaim", "pvc":
		return u.deletePVC(ctx, obj, namespace, deleteOptions)
	case "configmap":
		return u.deleteConfigMap(ctx, obj, namespace, deleteOptions)
	case "secret":
		return u.deleteSecret(ctx, obj, namespace, deleteOptions)
	case "ingress":
		return u.deleteIngress(ctx, obj, namespace, deleteOptions)
	default:
		return u.deleteGenericResource(ctx, obj, namespace, deleteOptions)
	}
}

// deleteDeployment deletes a Deployment resource
func (u *impl) deleteDeployment(ctx context.Context, obj *unstructured.Unstructured, namespace string, options metav1.DeleteOptions) (*DeletedResource, error) {
	deploymentClient := u.k8sClient.AppsV1().Deployments(namespace)

	if len(options.DryRun) > 0 {
		return &DeletedResource{
			Kind:      "Deployment",
			Name:      obj.GetName(),
			Namespace: namespace,
			Message:   "Deployment would be deleted (dry run)",
		}, nil
	}

	err := deploymentClient.Delete(ctx, obj.GetName(), options)
	if err != nil {
		if errors.IsNotFound(err) {
			return &DeletedResource{
				Kind:      "Deployment",
				Name:      obj.GetName(),
				Namespace: namespace,
				Message:   "Deployment not found, already deleted",
			}, nil
		}
		return nil, err
	}

	return &DeletedResource{
		Kind:      "Deployment",
		Name:      obj.GetName(),
		Namespace: namespace,
		Message:   "Deployment deleted successfully",
	}, nil
}

// deleteService deletes a Service resource
func (u *impl) deleteService(ctx context.Context, obj *unstructured.Unstructured, namespace string, options metav1.DeleteOptions) (*DeletedResource, error) {
	serviceClient := u.k8sClient.CoreV1().Services(namespace)

	if len(options.DryRun) > 0 {
		return &DeletedResource{
			Kind:      "Service",
			Name:      obj.GetName(),
			Namespace: namespace,
			Message:   "Service would be deleted (dry run)",
		}, nil
	}

	err := serviceClient.Delete(ctx, obj.GetName(), options)
	if err != nil {
		if errors.IsNotFound(err) {
			return &DeletedResource{
				Kind:      "Service",
				Name:      obj.GetName(),
				Namespace: namespace,
				Message:   "Service not found, already deleted",
			}, nil
		}
		return nil, err
	}

	return &DeletedResource{
		Kind:      "Service",
		Name:      obj.GetName(),
		Namespace: namespace,
		Message:   "Service deleted successfully",
	}, nil
}

// deletePVC deletes a PersistentVolumeClaim resource
func (u *impl) deletePVC(ctx context.Context, obj *unstructured.Unstructured, namespace string, options metav1.DeleteOptions) (*DeletedResource, error) {
	pvcClient := u.k8sClient.CoreV1().PersistentVolumeClaims(namespace)

	if len(options.DryRun) > 0 {
		return &DeletedResource{
			Kind:      "PersistentVolumeClaim",
			Name:      obj.GetName(),
			Namespace: namespace,
			Message:   "PersistentVolumeClaim would be deleted (dry run)",
		}, nil
	}

	err := pvcClient.Delete(ctx, obj.GetName(), options)
	if err != nil {
		if errors.IsNotFound(err) {
			return &DeletedResource{
				Kind:      "PersistentVolumeClaim",
				Name:      obj.GetName(),
				Namespace: namespace,
				Message:   "PersistentVolumeClaim not found, already deleted",
			}, nil
		}
		return nil, err
	}

	return &DeletedResource{
		Kind:      "PersistentVolumeClaim",
		Name:      obj.GetName(),
		Namespace: namespace,
		Message:   "PersistentVolumeClaim deleted successfully",
	}, nil
}

// deleteConfigMap deletes a ConfigMap resource
func (u *impl) deleteConfigMap(ctx context.Context, obj *unstructured.Unstructured, namespace string, options metav1.DeleteOptions) (*DeletedResource, error) {
	configMapClient := u.k8sClient.CoreV1().ConfigMaps(namespace)

	if len(options.DryRun) > 0 {
		return &DeletedResource{
			Kind:      "ConfigMap",
			Name:      obj.GetName(),
			Namespace: namespace,
			Message:   "ConfigMap would be deleted (dry run)",
		}, nil
	}

	err := configMapClient.Delete(ctx, obj.GetName(), options)
	if err != nil {
		if errors.IsNotFound(err) {
			return &DeletedResource{
				Kind:      "ConfigMap",
				Name:      obj.GetName(),
				Namespace: namespace,
				Message:   "ConfigMap not found, already deleted",
			}, nil
		}
		return nil, err
	}

	return &DeletedResource{
		Kind:      "ConfigMap",
		Name:      obj.GetName(),
		Namespace: namespace,
		Message:   "ConfigMap deleted successfully",
	}, nil
}

// deleteSecret deletes a Secret resource
func (u *impl) deleteSecret(ctx context.Context, obj *unstructured.Unstructured, namespace string, options metav1.DeleteOptions) (*DeletedResource, error) {
	secretClient := u.k8sClient.CoreV1().Secrets(namespace)

	if len(options.DryRun) > 0 {
		return &DeletedResource{
			Kind:      "Secret",
			Name:      obj.GetName(),
			Namespace: namespace,
			Message:   "Secret would be deleted (dry run)",
		}, nil
	}

	err := secretClient.Delete(ctx, obj.GetName(), options)
	if err != nil {
		if errors.IsNotFound(err) {
			return &DeletedResource{
				Kind:      "Secret",
				Name:      obj.GetName(),
				Namespace: namespace,
				Message:   "Secret not found, already deleted",
			}, nil
		}
		return nil, err
	}

	return &DeletedResource{
		Kind:      "Secret",
		Name:      obj.GetName(),
		Namespace: namespace,
		Message:   "Secret deleted successfully",
	}, nil
}

// deleteGenericResource deletes a generic Kubernetes resource using dynamic client
func (u *impl) deleteGenericResource(ctx context.Context, obj *unstructured.Unstructured, namespace string, options metav1.DeleteOptions) (*DeletedResource, error) {
	// For a complete implementation, you would use the dynamic client here
	// This is a simplified version that just returns a placeholder

	if len(options.DryRun) > 0 {
		return &DeletedResource{
			Kind:      obj.GetKind(),
			Name:      obj.GetName(),
			Namespace: namespace,
			Message:   fmt.Sprintf("%s would be deleted (dry run)", obj.GetKind()),
		}, nil
	}

	// In a real implementation, you would use the dynamic client to delete the resource
	// For now, we'll just return a placeholder
	return &DeletedResource{
		Kind:      obj.GetKind(),
		Name:      obj.GetName(),
		Namespace: namespace,
		Message:   fmt.Sprintf("%s deletion not fully implemented for generic resources", obj.GetKind()),
	}, nil
}

// deleteAllDeployments deletes all deployments with our tracking label in the specified namespace
func (u *impl) deleteAllDeployments(ctx context.Context, input DeleteKubernetesManifestsInput) ([]DeletedResource, error) {
	deploymentClient := u.k8sClient.AppsV1().Deployments(input.Namespace)

	// List all deployments with our label
	deployments, err := deploymentClient.List(ctx, metav1.ListOptions{
		LabelSelector: composeRepoIDLabelSelector(input.ComposeRepo.ID),
	})
	if err != nil {
		return nil, fmt.Errorf("failed to list deployments: %w", err)
	}

	var deletedResources []DeletedResource

	// Create delete options
	deleteOptions := metav1.DeleteOptions{}
	if input.DryRun {
		deleteOptions.DryRun = []string{"All"}
	}

	// Delete each deployment
	for _, deployment := range deployments.Items {
		if input.DryRun {
			deletedResources = append(deletedResources, DeletedResource{
				Kind:      "Deployment",
				Name:      deployment.Name,
				Namespace: input.Namespace,
				Message:   "Deployment would be deleted (dry run)",
			})
			continue
		}

		err := deploymentClient.Delete(ctx, deployment.Name, deleteOptions)
		if err != nil {
			if !errors.IsNotFound(err) {
				return deletedResources, fmt.Errorf("failed to delete deployment %s: %w", deployment.Name, err)
			}
		}

		deletedResources = append(deletedResources, DeletedResource{
			Kind:      "Deployment",
			Name:      deployment.Name,
			Namespace: input.Namespace,
			Message:   "Deployment deleted successfully",
		})
	}

	return deletedResources, nil
}

// deleteAllServices deletes all services with our tracking label in the specified namespace
func (u *impl) deleteAllServices(ctx context.Context, input DeleteKubernetesManifestsInput) ([]DeletedResource, error) {
	serviceClient := u.k8sClient.CoreV1().Services(input.Namespace)

	// List all services with our label
	services, err := serviceClient.List(ctx, metav1.ListOptions{
		LabelSelector: composeRepoIDLabelSelector(input.ComposeRepo.ID),
	})
	if err != nil {
		return nil, fmt.Errorf("failed to list services: %w", err)
	}

	var deletedResources []DeletedResource

	// Create delete options
	deleteOptions := metav1.DeleteOptions{}
	if input.DryRun {
		deleteOptions.DryRun = []string{"All"}
	}

	// Delete each service
	for _, service := range services.Items {
		if input.DryRun {
			deletedResources = append(deletedResources, DeletedResource{
				Kind:      "Service",
				Name:      service.Name,
				Namespace: input.Namespace,
				Message:   "Service would be deleted (dry run)",
			})
			continue
		}

		err := serviceClient.Delete(ctx, service.Name, deleteOptions)
		if err != nil {
			if !errors.IsNotFound(err) {
				return deletedResources, fmt.Errorf("failed to delete service %s: %w", service.Name, err)
			}
		}

		deletedResources = append(deletedResources, DeletedResource{
			Kind:      "Service",
			Name:      service.Name,
			Namespace: input.Namespace,
			Message:   "Service deleted successfully",
		})
	}

	return deletedResources, nil
}

// deleteAllConfigMaps deletes all configmaps with our tracking label in the specified namespace
func (u *impl) deleteAllConfigMaps(ctx context.Context, input DeleteKubernetesManifestsInput) ([]DeletedResource, error) {
	configMapClient := u.k8sClient.CoreV1().ConfigMaps(input.Namespace)

	// List all configmaps with our label
	configMaps, err := configMapClient.List(ctx, metav1.ListOptions{
		LabelSelector: composeRepoIDLabelSelector(input.ComposeRepo.ID),
	})
	if err != nil {
		return nil, fmt.Errorf("failed to list configmaps: %w", err)
	}

	var deletedResources []DeletedResource

	// Create delete options
	deleteOptions := metav1.DeleteOptions{}
	if input.DryRun {
		deleteOptions.DryRun = []string{"All"}
	}

	// Delete each configmap
	for _, configMap := range configMaps.Items {
		if input.DryRun {
			deletedResources = append(deletedResources, DeletedResource{
				Kind:      "ConfigMap",
				Name:      configMap.Name,
				Namespace: input.Namespace,
				Message:   "ConfigMap would be deleted (dry run)",
			})
			continue
		}

		err := configMapClient.Delete(ctx, configMap.Name, deleteOptions)
		if err != nil {
			if !errors.IsNotFound(err) {
				return deletedResources, fmt.Errorf("failed to delete configmap %s: %w", configMap.Name, err)
			}
		}

		deletedResources = append(deletedResources, DeletedResource{
			Kind:      "ConfigMap",
			Name:      configMap.Name,
			Namespace: input.Namespace,
			Message:   "ConfigMap deleted successfully",
		})
	}

	return deletedResources, nil
}

// deleteAllPVCs deletes all PVCs with our tracking label in the specified namespace
func (u *impl) deleteAllPVCs(ctx context.Context, input DeleteKubernetesManifestsInput) ([]DeletedResource, error) {
	pvcClient := u.k8sClient.CoreV1().PersistentVolumeClaims(input.Namespace)

	// List all PVCs with our label
	pvcs, err := pvcClient.List(ctx, metav1.ListOptions{
		LabelSelector: composeRepoIDLabelSelector(input.ComposeRepo.ID),
	})
	if err != nil {
		return nil, fmt.Errorf("failed to list pvcs: %w", err)
	}

	var deletedResources []DeletedResource

	// Create delete options
	deleteOptions := metav1.DeleteOptions{}
	if input.DryRun {
		deleteOptions.DryRun = []string{"All"}
	}

	// Delete each PVC
	for _, pvc := range pvcs.Items {
		if input.DryRun {
			deletedResources = append(deletedResources, DeletedResource{
				Kind:      "PersistentVolumeClaim",
				Name:      pvc.Name,
				Namespace: input.Namespace,
				Message:   "PersistentVolumeClaim would be deleted (dry run)",
			})
			continue
		}

		err := pvcClient.Delete(ctx, pvc.Name, deleteOptions)
		if err != nil {
			if !errors.IsNotFound(err) {
				return deletedResources, fmt.Errorf("failed to delete pvc %s: %w", pvc.Name, err)
			}
		}

		deletedResources = append(deletedResources, DeletedResource{
			Kind:      "PersistentVolumeClaim",
			Name:      pvc.Name,
			Namespace: input.Namespace,
			Message:   "PersistentVolumeClaim deleted successfully",
		})
	}

	return deletedResources, nil
}

// deleteAllSecrets deletes all secrets with our tracking label in the specified namespace
func (u *impl) deleteAllSecrets(ctx context.Context, input DeleteKubernetesManifestsInput) ([]DeletedResource, error) {
	secretClient := u.k8sClient.CoreV1().Secrets(input.Namespace)

	// List all secrets with our label
	secrets, err := secretClient.List(ctx, metav1.ListOptions{
		LabelSelector: composeRepoIDLabelSelector(input.ComposeRepo.ID),
	})
	if err != nil {
		return nil, fmt.Errorf("failed to list secrets: %w", err)
	}

	var deletedResources []DeletedResource

	// Create delete options
	deleteOptions := metav1.DeleteOptions{}
	if input.DryRun {
		deleteOptions.DryRun = []string{"All"}
	}

	// Delete each secret
	for _, secret := range secrets.Items {
		if input.DryRun {
			deletedResources = append(deletedResources, DeletedResource{
				Kind:      "Secret",
				Name:      secret.Name,
				Namespace: input.Namespace,
				Message:   "Secret would be deleted (dry run)",
			})
			continue
		}

		err := secretClient.Delete(ctx, secret.Name, deleteOptions)
		if err != nil {
			if !errors.IsNotFound(err) {
				return deletedResources, fmt.Errorf("failed to delete secret %s: %w", secret.Name, err)
			}
		}

		deletedResources = append(deletedResources, DeletedResource{
			Kind:      "Secret",
			Name:      secret.Name,
			Namespace: input.Namespace,
			Message:   "Secret deleted successfully",
		})
	}

	return deletedResources, nil
}

// delete a specific namespace
func (u *impl) deleteNamespace(ctx context.Context, input DeleteKubernetesManifestsInput) (*DeletedResource, error) {
	namespaceClient := u.k8sClient.CoreV1().Namespaces()

	// Create delete options
	deleteOptions := metav1.DeleteOptions{}
	if input.DryRun {
		deleteOptions.DryRun = []string{"All"}
	}

	// Delete the namespace
	if input.DryRun {
		return &DeletedResource{
			Kind:      "Namespace",
			Name:      input.Namespace,
			Namespace: input.Namespace,
			Message:   "Namespace would be deleted (dry run)",
		}, nil
	}

	err := namespaceClient.Delete(ctx, input.Namespace, deleteOptions)
	if err != nil {
		if !errors.IsNotFound(err) {
			return nil, fmt.Errorf("failed to delete namespace %s: %w", input.Namespace, err)
		}
	}

	return &DeletedResource{
		Kind:      "Namespace",
		Name:      input.Namespace,
		Namespace: input.Namespace,
		Message:   "Namespace deleted successfully",
	}, nil
}

// deleteIngress deletes an Ingress resource
func (u *impl) deleteIngress(ctx context.Context, obj *unstructured.Unstructured, namespace string, options metav1.DeleteOptions) (*DeletedResource, error) {
	ingressClient := u.k8sClient.NetworkingV1().Ingresses(namespace)

	if len(options.DryRun) > 0 {
		return &DeletedResource{
			Kind:      "Ingress",
			Name:      obj.GetName(),
			Namespace: namespace,
			Message:   "Ingress would be deleted (dry run)",
		}, nil
	}

	err := ingressClient.Delete(ctx, obj.GetName(), options)
	if err != nil {
		if errors.IsNotFound(err) {
			return &DeletedResource{
				Kind:      "Ingress",
				Name:      obj.GetName(),
				Namespace: namespace,
				Message:   "Ingress not found, already deleted",
			}, nil
		}
		return nil, err
	}

	return &DeletedResource{
		Kind:      "Ingress",
		Name:      obj.GetName(),
		Namespace: namespace,
		Message:   "Ingress deleted successfully",
	}, nil
}

// deleteAllIngresses deletes all Ingress resources in the specified namespace
func (u *impl) deleteAllIngresses(ctx context.Context, input DeleteKubernetesManifestsInput) ([]DeletedResource, error) {
	ingressClient := u.k8sClient.NetworkingV1().Ingresses(input.Namespace)

	// List all ingresses in the namespace
	ingresses, err := ingressClient.List(ctx, metav1.ListOptions{
		LabelSelector: composeRepoIDLabelSelector(input.ComposeRepo.ID),
	})
	if err != nil {
		return nil, fmt.Errorf("failed to list ingresses: %w", err)
	}

	var deletedResources []DeletedResource
	var deleteOptions metav1.DeleteOptions
	if input.DryRun {
		deleteOptions.DryRun = []string{"All"}
	}

	for _, ingress := range ingresses.Items {
		if input.DryRun {
			deletedResources = append(deletedResources, DeletedResource{
				Kind:      "Ingress",
				Name:      ingress.Name,
				Namespace: input.Namespace,
				Message:   "Ingress would be deleted (dry run)",
			})
			continue
		}

		err := ingressClient.Delete(ctx, ingress.Name, deleteOptions)
		if err != nil {
			if !errors.IsNotFound(err) {
				return deletedResources, fmt.Errorf("failed to delete ingress %s: %w", ingress.Name, err)
			}
		}

		deletedResources = append(deletedResources, DeletedResource{
			Kind:      "Ingress",
			Name:      ingress.Name,
			Namespace: input.Namespace,
			Message:   "Ingress deleted successfully",
		})
	}

	return deletedResources, nil
}

func composeRepoName(repoName string) string {
	return fmt.Sprintf("compose-%s", repoName)
}

func composeRepoIDLabelSelector(repoID uuid.UUID) string {
	return fmt.Sprintf("compose-repo-id=%s", composeRepoName(repoID.String()))
}

// watch for Compose event that emit from Compose Namespace operator
// func watchComposeNamespaceOperatorEvent(ctx context.Context, clientset kubernetes.Interface, namespace string) error {
// 	ctx, span := oteltrace.Tracer.Start(ctx, "usecase.kompose.watchComposeEvent")
// 	defer span.End()
//
// 	fieldSelector := fmt.Sprintf("involvedObject.name=%s", namespace)
// 	watcher, err := clientset.CoreV1().Events("").Watch(ctx, metav1.ListOptions{
// 		FieldSelector:        fieldSelector,
// 		Watch:                true,
// 		SendInitialEvents:    utils.Ptr(true),
// 		ResourceVersionMatch: "NotOlderThan",
// 	})
// 	if err != nil {
// 		panic(err)
// 	}
//
// 	span.AddEvent("Watching for Compose events", trace.WithAttributes(attribute.String("field_selector", fieldSelector)))
//
// 	// implement a timer
// 	timer := time.NewTimer(10 * time.Second)
// 	defer timer.Stop()
// 	for {
// 		select {
// 		case <-timer.C:
// 			return fmt.Errorf("timeout waiting for ComposeCompleted event")
// 		case <-ctx.Done():
// 			return ctx.Err()
// 		case event := <-watcher.ResultChan():
// 			kubeEvent, ok := event.Object.(*corev1.Event)
// 			if !ok {
// 				continue
// 			}
//
// 			span.AddEvent("Compose event",
// 				trace.WithAttributes(
// 					attribute.String("type", kubeEvent.Type),
// 					attribute.String("kind", kubeEvent.InvolvedObject.Kind),
// 					attribute.String("name", kubeEvent.InvolvedObject.Name),
// 					attribute.String("reason", kubeEvent.Reason),
// 					attribute.String("message", kubeEvent.Message),
// 				),
// 			)
//
// 			if kubeEvent.Reason == "ComposeCompleted" {
// 				// deployment is ready, we can stop watching
// 				return nil
// 			}
// 		}
// 	}
// }

// pollComposeRoleBinding polls for the existence of the compose-role-binding RoleBinding
// in the specified namespace. It polls every 1 second and fails after 10 attempts.
func pollComposeRoleBinding(ctx context.Context, clientset kubernetes.Interface, namespace string) error {
	ctx, span := oteltrace.Tracer.Start(ctx, "usecase.kompose.pollComposeRoleBinding")
	defer span.End()

	span.AddEvent("Polling for compose-deployment-manager-role-binding", trace.WithAttributes(attribute.String("namespace", namespace)))

	for attempt := 1; attempt <= POLL_COMPOSE_ROLE_BINDING_MAX_ATTEMPTS; attempt++ {
		span.AddEvent("Polling attempt", trace.WithAttributes(attribute.Int("attempt", attempt)))

		roleBinding, err := clientset.RbacV1().RoleBindings(namespace).Get(ctx, COMPOSE_DEPLOYMENT_MANAGER_ROLE_BINDING, metav1.GetOptions{})
		if err == nil && roleBinding != nil {
			span.AddEvent("RoleBinding found", trace.WithAttributes(
				attribute.String("name", roleBinding.Name),
				attribute.String("namespace", roleBinding.Namespace),
			))
			return nil
		}

		// If we've reached the max attempts, return an error
		if attempt == POLL_COMPOSE_ROLE_BINDING_MAX_ATTEMPTS {
			return fmt.Errorf(
				"timed out waiting for RoleBinding %s in namespace %s after %d attempts",
				COMPOSE_DEPLOYMENT_MANAGER_ROLE_BINDING,
				namespace,
				POLL_COMPOSE_ROLE_BINDING_MAX_ATTEMPTS,
			)
		}

		// Wait before the next attempt
		select {
		case <-ctx.Done():
			return ctx.Err()
		case <-time.After(POLL_COMPOSE_ROLE_BINDING_INTERVAL):
			// Continue to next attempt
		}
	}

	// This should never be reached due to the return in the loop
	return fmt.Errorf(
		"unexpected error polling for RoleBinding %s in namespace %s",
		COMPOSE_DEPLOYMENT_MANAGER_ROLE_BINDING,
		namespace,
	)
}

// applyResourceRequirements applies resource requests and limits to a Deployment
func applyResourceRequirements(deployment *appsv1.Deployment, resourceReqMap map[string]*ResourceRequirement) error {
	// Ensure we have containers in the pod spec
	if len(deployment.Spec.Template.Spec.Containers) == 0 {
		return fmt.Errorf("deployment has no containers")
	}

	resourceReq := &ResourceRequirement{
		Requests: &ResourceSpec{
			CPU:    utils.Ptr("1"),
			Memory: defaultMemory,
		},
		// Limits: &ResourceSpec{
		// 	CPU:    utils.Ptr("200m"),
		// 	Memory: utils.Ptr("256Mi"),
		// },
	}
	if resourceReqMap != nil {
		if value, exists := resourceReqMap[deployment.Name]; exists && value != nil {
			if value.Requests != nil {
				if value.Requests.CPU != nil {
					resourceReq.Requests.CPU = value.Requests.CPU
				}
				if value.Requests.Memory != nil {
					resourceReq.Requests.Memory = value.Requests.Memory
				}
				if value.Requests.GPU != nil {
					resourceReq.Requests.GPU = value.Requests.GPU
				}
			}

			if value.Limits != nil {
				resourceReq.Limits = value.Limits
				// if no GPU and memory limit is specified, set default memory limit
				if value.Limits.GPU == nil && value.Limits.Memory == nil {
					resourceReq.Limits.Memory = defaultMemory
				}
			}
		}
	}

	// Apply resource requirements to the first container (main container)
	container := &deployment.Spec.Template.Spec.Containers[0]

	// Initialize resources if not present
	if container.Resources.Requests == nil {
		container.Resources.Requests = make(corev1.ResourceList)
	}
	if container.Resources.Limits == nil {
		container.Resources.Limits = make(corev1.ResourceList)
	}

	// Apply resource requests
	if resourceReq.Requests != nil {
		if resourceReq.Requests.CPU != nil {
			cpuQuantity, err := resource.ParseQuantity(*resourceReq.Requests.CPU)
			if err != nil {
				return fmt.Errorf("invalid CPU request value '%s': %w", *resourceReq.Requests.CPU, err)
			}
			container.Resources.Requests[corev1.ResourceCPU] = cpuQuantity
		}

		if resourceReq.Requests.Memory != nil {
			memoryQuantity, err := resource.ParseQuantity(*resourceReq.Requests.Memory)
			if err != nil {
				return fmt.Errorf("invalid memory request value '%s': %w", *resourceReq.Requests.Memory, err)
			}
			container.Resources.Requests[corev1.ResourceMemory] = memoryQuantity
		}

		if resourceReq.Requests.GPU != nil {
			gpuQuantity, err := resource.ParseQuantity(*resourceReq.Requests.GPU)
			if err != nil {
				return fmt.Errorf("invalid GPU request value '%s': %w", *resourceReq.Requests.GPU, err)
			}
			container.Resources.Requests[enums.NvidiaGPUResourceName] = gpuQuantity
		}
	}

	// Apply resource limits
	if resourceReq.Limits != nil {
		if resourceReq.Limits.CPU != nil {
			cpuQuantity, err := resource.ParseQuantity(*resourceReq.Limits.CPU)
			if err != nil {
				return fmt.Errorf("invalid CPU limit value '%s': %w", *resourceReq.Limits.CPU, err)
			}
			container.Resources.Limits[corev1.ResourceCPU] = cpuQuantity
		}

		if resourceReq.Limits.Memory != nil {
			memoryQuantity, err := resource.ParseQuantity(*resourceReq.Limits.Memory)
			if err != nil {
				return fmt.Errorf("invalid memory limit value '%s': %w", *resourceReq.Limits.Memory, err)
			}
			container.Resources.Limits[corev1.ResourceMemory] = memoryQuantity
		}

		if resourceReq.Limits.GPU != nil {
			gpuQuantity, err := resource.ParseQuantity(*resourceReq.Limits.GPU)
			if err != nil {
				return fmt.Errorf("invalid GPU limit value '%s': %w", *resourceReq.Limits.GPU, err)
			}
			container.Resources.Limits[enums.NvidiaGPUResourceName] = gpuQuantity
		}
	}

	return nil
}

func applyAppSelector(deployment *appsv1.Deployment, app string) {
	if deployment.Spec.Selector == nil {
		deployment.Spec.Selector = &metav1.LabelSelector{
			MatchLabels: map[string]string{
				"app": app,
			},
		}
	} else {
		deployment.Spec.Selector.MatchLabels["app"] = app
	}
}

// applyGPURequirements applies GPU requirements, tolerations, and node selectors to a Deployment
func applyGPURequirements(deployment *appsv1.Deployment, gpuReqMap map[string]*GPURequirement) error {
	if gpuReqMap == nil {
		return nil
	}

	// Find GPU requirement for this deployment
	var gpuReq *GPURequirement
	if value, exists := gpuReqMap[deployment.Name]; exists && value != nil {
		gpuReq = value
	}

	// If no GPU requirement or GPU is disabled, skip
	if gpuReq == nil || !gpuReq.Enabled {
		return nil
	}

	// Apply tolerations
	if len(gpuReq.Tolerations) > 0 {
		tolerations := make([]corev1.Toleration, len(gpuReq.Tolerations))
		for i, gpuTol := range gpuReq.Tolerations {
			tolerations[i] = corev1.Toleration{
				Key:      gpuTol.Key,
				Operator: corev1.TolerationOperator(gpuTol.Operator),
				Effect:   corev1.TaintEffect(gpuTol.Effect),
			}
		}
		deployment.Spec.Template.Spec.Tolerations = append(deployment.Spec.Template.Spec.Tolerations, tolerations...)
	}

	// Apply node selector
	if gpuReq.NodeSelector != nil {
		if deployment.Spec.Template.Spec.NodeSelector == nil {
			deployment.Spec.Template.Spec.NodeSelector = make(map[string]string)
		}
		deployment.Spec.Template.Spec.NodeSelector = gpuReq.NodeSelector
	}

	return nil
}
