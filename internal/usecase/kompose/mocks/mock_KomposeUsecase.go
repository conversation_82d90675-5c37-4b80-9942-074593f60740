// Code generated by mockery v2.53.4. DO NOT EDIT.

package mocks

import (
	dto "api-server/internal/dto"
	kompose "api-server/internal/usecase/kompose"
	context "context"

	mock "github.com/stretchr/testify/mock"

	types "api-server/internal/types"

	uuid "github.com/google/uuid"
)

// MockKomposeUsecase is an autogenerated mock type for the KomposeUsecase type
type MockKomposeUsecase struct {
	mock.Mock
}

type MockKomposeUsecase_Expecter struct {
	mock *mock.Mock
}

func (_m *MockKomposeUsecase) EXPECT() *MockKomposeUsecase_Expecter {
	return &MockKomposeUsecase_Expecter{mock: &_m.Mock}
}

// ApplyKubernetesManifests provides a mock function with given fields: ctx, input
func (_m *MockKomposeUsecase) ApplyKubernetesManifests(ctx context.Context, input kompose.ApplyKubernetesManifestsInput) (*kompose.ApplyKubernetesManifestsResponse, error) {
	ret := _m.Called(ctx, input)

	if len(ret) == 0 {
		panic("no return value specified for ApplyKubernetesManifests")
	}

	var r0 *kompose.ApplyKubernetesManifestsResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, kompose.ApplyKubernetesManifestsInput) (*kompose.ApplyKubernetesManifestsResponse, error)); ok {
		return rf(ctx, input)
	}
	if rf, ok := ret.Get(0).(func(context.Context, kompose.ApplyKubernetesManifestsInput) *kompose.ApplyKubernetesManifestsResponse); ok {
		r0 = rf(ctx, input)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*kompose.ApplyKubernetesManifestsResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, kompose.ApplyKubernetesManifestsInput) error); ok {
		r1 = rf(ctx, input)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockKomposeUsecase_ApplyKubernetesManifests_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ApplyKubernetesManifests'
type MockKomposeUsecase_ApplyKubernetesManifests_Call struct {
	*mock.Call
}

// ApplyKubernetesManifests is a helper method to define mock.On call
//   - ctx context.Context
//   - input kompose.ApplyKubernetesManifestsInput
func (_e *MockKomposeUsecase_Expecter) ApplyKubernetesManifests(ctx interface{}, input interface{}) *MockKomposeUsecase_ApplyKubernetesManifests_Call {
	return &MockKomposeUsecase_ApplyKubernetesManifests_Call{Call: _e.mock.On("ApplyKubernetesManifests", ctx, input)}
}

func (_c *MockKomposeUsecase_ApplyKubernetesManifests_Call) Run(run func(ctx context.Context, input kompose.ApplyKubernetesManifestsInput)) *MockKomposeUsecase_ApplyKubernetesManifests_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(kompose.ApplyKubernetesManifestsInput))
	})
	return _c
}

func (_c *MockKomposeUsecase_ApplyKubernetesManifests_Call) Return(_a0 *kompose.ApplyKubernetesManifestsResponse, _a1 error) *MockKomposeUsecase_ApplyKubernetesManifests_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockKomposeUsecase_ApplyKubernetesManifests_Call) RunAndReturn(run func(context.Context, kompose.ApplyKubernetesManifestsInput) (*kompose.ApplyKubernetesManifestsResponse, error)) *MockKomposeUsecase_ApplyKubernetesManifests_Call {
	_c.Call.Return(run)
	return _c
}

// ConvertDockerComposeToKubernetes provides a mock function with given fields: ctx, input
func (_m *MockKomposeUsecase) ConvertDockerComposeToKubernetes(ctx context.Context, input kompose.ConvertDockerComposeInput) (*kompose.ConvertDockerComposeResponse, error) {
	ret := _m.Called(ctx, input)

	if len(ret) == 0 {
		panic("no return value specified for ConvertDockerComposeToKubernetes")
	}

	var r0 *kompose.ConvertDockerComposeResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, kompose.ConvertDockerComposeInput) (*kompose.ConvertDockerComposeResponse, error)); ok {
		return rf(ctx, input)
	}
	if rf, ok := ret.Get(0).(func(context.Context, kompose.ConvertDockerComposeInput) *kompose.ConvertDockerComposeResponse); ok {
		r0 = rf(ctx, input)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*kompose.ConvertDockerComposeResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, kompose.ConvertDockerComposeInput) error); ok {
		r1 = rf(ctx, input)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockKomposeUsecase_ConvertDockerComposeToKubernetes_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ConvertDockerComposeToKubernetes'
type MockKomposeUsecase_ConvertDockerComposeToKubernetes_Call struct {
	*mock.Call
}

// ConvertDockerComposeToKubernetes is a helper method to define mock.On call
//   - ctx context.Context
//   - input kompose.ConvertDockerComposeInput
func (_e *MockKomposeUsecase_Expecter) ConvertDockerComposeToKubernetes(ctx interface{}, input interface{}) *MockKomposeUsecase_ConvertDockerComposeToKubernetes_Call {
	return &MockKomposeUsecase_ConvertDockerComposeToKubernetes_Call{Call: _e.mock.On("ConvertDockerComposeToKubernetes", ctx, input)}
}

func (_c *MockKomposeUsecase_ConvertDockerComposeToKubernetes_Call) Run(run func(ctx context.Context, input kompose.ConvertDockerComposeInput)) *MockKomposeUsecase_ConvertDockerComposeToKubernetes_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(kompose.ConvertDockerComposeInput))
	})
	return _c
}

func (_c *MockKomposeUsecase_ConvertDockerComposeToKubernetes_Call) Return(_a0 *kompose.ConvertDockerComposeResponse, _a1 error) *MockKomposeUsecase_ConvertDockerComposeToKubernetes_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockKomposeUsecase_ConvertDockerComposeToKubernetes_Call) RunAndReturn(run func(context.Context, kompose.ConvertDockerComposeInput) (*kompose.ConvertDockerComposeResponse, error)) *MockKomposeUsecase_ConvertDockerComposeToKubernetes_Call {
	_c.Call.Return(run)
	return _c
}

// CreateComposeDeployment provides a mock function with given fields: ctx, userID, repoID, input
func (_m *MockKomposeUsecase) CreateComposeDeployment(ctx context.Context, userID uuid.UUID, repoID types.RepoID, input dto.StartDeploymentRequest) (*dto.StartDeploymentResponse, error) {
	ret := _m.Called(ctx, userID, repoID, input)

	if len(ret) == 0 {
		panic("no return value specified for CreateComposeDeployment")
	}

	var r0 *dto.StartDeploymentResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, uuid.UUID, types.RepoID, dto.StartDeploymentRequest) (*dto.StartDeploymentResponse, error)); ok {
		return rf(ctx, userID, repoID, input)
	}
	if rf, ok := ret.Get(0).(func(context.Context, uuid.UUID, types.RepoID, dto.StartDeploymentRequest) *dto.StartDeploymentResponse); ok {
		r0 = rf(ctx, userID, repoID, input)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*dto.StartDeploymentResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, uuid.UUID, types.RepoID, dto.StartDeploymentRequest) error); ok {
		r1 = rf(ctx, userID, repoID, input)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockKomposeUsecase_CreateComposeDeployment_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CreateComposeDeployment'
type MockKomposeUsecase_CreateComposeDeployment_Call struct {
	*mock.Call
}

// CreateComposeDeployment is a helper method to define mock.On call
//   - ctx context.Context
//   - userID uuid.UUID
//   - repoID types.RepoID
//   - input dto.StartDeploymentRequest
func (_e *MockKomposeUsecase_Expecter) CreateComposeDeployment(ctx interface{}, userID interface{}, repoID interface{}, input interface{}) *MockKomposeUsecase_CreateComposeDeployment_Call {
	return &MockKomposeUsecase_CreateComposeDeployment_Call{Call: _e.mock.On("CreateComposeDeployment", ctx, userID, repoID, input)}
}

func (_c *MockKomposeUsecase_CreateComposeDeployment_Call) Run(run func(ctx context.Context, userID uuid.UUID, repoID types.RepoID, input dto.StartDeploymentRequest)) *MockKomposeUsecase_CreateComposeDeployment_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(uuid.UUID), args[2].(types.RepoID), args[3].(dto.StartDeploymentRequest))
	})
	return _c
}

func (_c *MockKomposeUsecase_CreateComposeDeployment_Call) Return(_a0 *dto.StartDeploymentResponse, _a1 error) *MockKomposeUsecase_CreateComposeDeployment_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockKomposeUsecase_CreateComposeDeployment_Call) RunAndReturn(run func(context.Context, uuid.UUID, types.RepoID, dto.StartDeploymentRequest) (*dto.StartDeploymentResponse, error)) *MockKomposeUsecase_CreateComposeDeployment_Call {
	_c.Call.Return(run)
	return _c
}

// DeleteKubernetesManifests provides a mock function with given fields: ctx, input
func (_m *MockKomposeUsecase) DeleteKubernetesManifests(ctx context.Context, input kompose.DeleteKubernetesManifestsInput) (*kompose.DeleteKubernetesManifestsResponse, error) {
	ret := _m.Called(ctx, input)

	if len(ret) == 0 {
		panic("no return value specified for DeleteKubernetesManifests")
	}

	var r0 *kompose.DeleteKubernetesManifestsResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, kompose.DeleteKubernetesManifestsInput) (*kompose.DeleteKubernetesManifestsResponse, error)); ok {
		return rf(ctx, input)
	}
	if rf, ok := ret.Get(0).(func(context.Context, kompose.DeleteKubernetesManifestsInput) *kompose.DeleteKubernetesManifestsResponse); ok {
		r0 = rf(ctx, input)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*kompose.DeleteKubernetesManifestsResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, kompose.DeleteKubernetesManifestsInput) error); ok {
		r1 = rf(ctx, input)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockKomposeUsecase_DeleteKubernetesManifests_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'DeleteKubernetesManifests'
type MockKomposeUsecase_DeleteKubernetesManifests_Call struct {
	*mock.Call
}

// DeleteKubernetesManifests is a helper method to define mock.On call
//   - ctx context.Context
//   - input kompose.DeleteKubernetesManifestsInput
func (_e *MockKomposeUsecase_Expecter) DeleteKubernetesManifests(ctx interface{}, input interface{}) *MockKomposeUsecase_DeleteKubernetesManifests_Call {
	return &MockKomposeUsecase_DeleteKubernetesManifests_Call{Call: _e.mock.On("DeleteKubernetesManifests", ctx, input)}
}

func (_c *MockKomposeUsecase_DeleteKubernetesManifests_Call) Run(run func(ctx context.Context, input kompose.DeleteKubernetesManifestsInput)) *MockKomposeUsecase_DeleteKubernetesManifests_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(kompose.DeleteKubernetesManifestsInput))
	})
	return _c
}

func (_c *MockKomposeUsecase_DeleteKubernetesManifests_Call) Return(_a0 *kompose.DeleteKubernetesManifestsResponse, _a1 error) *MockKomposeUsecase_DeleteKubernetesManifests_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockKomposeUsecase_DeleteKubernetesManifests_Call) RunAndReturn(run func(context.Context, kompose.DeleteKubernetesManifestsInput) (*kompose.DeleteKubernetesManifestsResponse, error)) *MockKomposeUsecase_DeleteKubernetesManifests_Call {
	_c.Call.Return(run)
	return _c
}

// ListComposeDeployment provides a mock function with given fields: ctx, input
func (_m *MockKomposeUsecase) ListComposeDeployment(ctx context.Context, input dto.ListComposeDeploymentInput) ([]dto.ComposeService, int64, error) {
	ret := _m.Called(ctx, input)

	if len(ret) == 0 {
		panic("no return value specified for ListComposeDeployment")
	}

	var r0 []dto.ComposeService
	var r1 int64
	var r2 error
	if rf, ok := ret.Get(0).(func(context.Context, dto.ListComposeDeploymentInput) ([]dto.ComposeService, int64, error)); ok {
		return rf(ctx, input)
	}
	if rf, ok := ret.Get(0).(func(context.Context, dto.ListComposeDeploymentInput) []dto.ComposeService); ok {
		r0 = rf(ctx, input)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]dto.ComposeService)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, dto.ListComposeDeploymentInput) int64); ok {
		r1 = rf(ctx, input)
	} else {
		r1 = ret.Get(1).(int64)
	}

	if rf, ok := ret.Get(2).(func(context.Context, dto.ListComposeDeploymentInput) error); ok {
		r2 = rf(ctx, input)
	} else {
		r2 = ret.Error(2)
	}

	return r0, r1, r2
}

// MockKomposeUsecase_ListComposeDeployment_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ListComposeDeployment'
type MockKomposeUsecase_ListComposeDeployment_Call struct {
	*mock.Call
}

// ListComposeDeployment is a helper method to define mock.On call
//   - ctx context.Context
//   - input dto.ListComposeDeploymentInput
func (_e *MockKomposeUsecase_Expecter) ListComposeDeployment(ctx interface{}, input interface{}) *MockKomposeUsecase_ListComposeDeployment_Call {
	return &MockKomposeUsecase_ListComposeDeployment_Call{Call: _e.mock.On("ListComposeDeployment", ctx, input)}
}

func (_c *MockKomposeUsecase_ListComposeDeployment_Call) Run(run func(ctx context.Context, input dto.ListComposeDeploymentInput)) *MockKomposeUsecase_ListComposeDeployment_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(dto.ListComposeDeploymentInput))
	})
	return _c
}

func (_c *MockKomposeUsecase_ListComposeDeployment_Call) Return(_a0 []dto.ComposeService, _a1 int64, _a2 error) *MockKomposeUsecase_ListComposeDeployment_Call {
	_c.Call.Return(_a0, _a1, _a2)
	return _c
}

func (_c *MockKomposeUsecase_ListComposeDeployment_Call) RunAndReturn(run func(context.Context, dto.ListComposeDeploymentInput) ([]dto.ComposeService, int64, error)) *MockKomposeUsecase_ListComposeDeployment_Call {
	_c.Call.Return(run)
	return _c
}

// ListEFS provides a mock function with given fields: ctx, repoID
func (_m *MockKomposeUsecase) ListEFS(ctx context.Context, repoID types.RepoID) ([]dto.EFS, error) {
	ret := _m.Called(ctx, repoID)

	if len(ret) == 0 {
		panic("no return value specified for ListEFS")
	}

	var r0 []dto.EFS
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, types.RepoID) ([]dto.EFS, error)); ok {
		return rf(ctx, repoID)
	}
	if rf, ok := ret.Get(0).(func(context.Context, types.RepoID) []dto.EFS); ok {
		r0 = rf(ctx, repoID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]dto.EFS)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, types.RepoID) error); ok {
		r1 = rf(ctx, repoID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockKomposeUsecase_ListEFS_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ListEFS'
type MockKomposeUsecase_ListEFS_Call struct {
	*mock.Call
}

// ListEFS is a helper method to define mock.On call
//   - ctx context.Context
//   - repoID types.RepoID
func (_e *MockKomposeUsecase_Expecter) ListEFS(ctx interface{}, repoID interface{}) *MockKomposeUsecase_ListEFS_Call {
	return &MockKomposeUsecase_ListEFS_Call{Call: _e.mock.On("ListEFS", ctx, repoID)}
}

func (_c *MockKomposeUsecase_ListEFS_Call) Run(run func(ctx context.Context, repoID types.RepoID)) *MockKomposeUsecase_ListEFS_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(types.RepoID))
	})
	return _c
}

func (_c *MockKomposeUsecase_ListEFS_Call) Return(_a0 []dto.EFS, _a1 error) *MockKomposeUsecase_ListEFS_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockKomposeUsecase_ListEFS_Call) RunAndReturn(run func(context.Context, types.RepoID) ([]dto.EFS, error)) *MockKomposeUsecase_ListEFS_Call {
	_c.Call.Return(run)
	return _c
}

// StopComposeDeployment provides a mock function with given fields: ctx, repoID
func (_m *MockKomposeUsecase) StopComposeDeployment(ctx context.Context, repoID types.RepoID) error {
	ret := _m.Called(ctx, repoID)

	if len(ret) == 0 {
		panic("no return value specified for StopComposeDeployment")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, types.RepoID) error); ok {
		r0 = rf(ctx, repoID)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MockKomposeUsecase_StopComposeDeployment_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'StopComposeDeployment'
type MockKomposeUsecase_StopComposeDeployment_Call struct {
	*mock.Call
}

// StopComposeDeployment is a helper method to define mock.On call
//   - ctx context.Context
//   - repoID types.RepoID
func (_e *MockKomposeUsecase_Expecter) StopComposeDeployment(ctx interface{}, repoID interface{}) *MockKomposeUsecase_StopComposeDeployment_Call {
	return &MockKomposeUsecase_StopComposeDeployment_Call{Call: _e.mock.On("StopComposeDeployment", ctx, repoID)}
}

func (_c *MockKomposeUsecase_StopComposeDeployment_Call) Run(run func(ctx context.Context, repoID types.RepoID)) *MockKomposeUsecase_StopComposeDeployment_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(types.RepoID))
	})
	return _c
}

func (_c *MockKomposeUsecase_StopComposeDeployment_Call) Return(_a0 error) *MockKomposeUsecase_StopComposeDeployment_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockKomposeUsecase_StopComposeDeployment_Call) RunAndReturn(run func(context.Context, types.RepoID) error) *MockKomposeUsecase_StopComposeDeployment_Call {
	_c.Call.Return(run)
	return _c
}

// NewMockKomposeUsecase creates a new instance of MockKomposeUsecase. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewMockKomposeUsecase(t interface {
	mock.TestingT
	Cleanup(func())
}) *MockKomposeUsecase {
	mock := &MockKomposeUsecase{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
