package handlers

import (
	"errors"
	"net/http"

	"github.com/gin-gonic/gin"
	"go.opentelemetry.io/otel/codes"

	"api-server/internal/dto"
	"api-server/internal/enums"
	"api-server/internal/types"
	"api-server/pkg/oteltrace"
	"api-server/pkg/validator"
)

// GetComposeServices godoc
//
//	@Summary	Get compose services
//	@Tags		Repository Kompose
//	@Accept		json
//	@Produce	json
//	@Param		repo_type	path		string							true	"Repository type"	Enums(spaces, datasets, models, composes)
//	@Param		namespace	path		string							true	"Repository namespace"
//	@Param		repo_name	path		string							true	"Repository name"
//	@Success	200			{array}		dto.GetComposeServicesResponse	"Ok"
//	@Failure	400			{object}	dto.HTTPError					"Bad Request - invalid request"
//	@Failure	404			{object}	dto.HTTPError					"Not Found"
//	@Failure	500			{object}	dto.HTTPError					"Internal Server Error"
//	@Router		/repositories/{repo_type}/{namespace}/{repo_name}/composes [get]
//
//	@Security	Bearer
func (h *repositoryHandlerImpl) GetComposeServices(c *gin.Context) {
	ctx, span := oteltrace.Tracer.Start(c.Request.Context(), "handlers.repository_kompose.GetComposeServices")
	defer span.End()

	repoID, err := types.RepoID{}.FromGinContext(c)
	if err != nil {
		span.SetStatus(codes.Error, "invalid repository ID")
		span.RecordError(err)
		dto.ErrorResponse(c, dto.NewBadRequestError(err, err.Error()))
		return
	}

	if *repoID.RepoType() != enums.RepoType_Composes {
		span.SetStatus(codes.Error, "not compose repository")
		span.RecordError(errors.New("not compose repository"))
		dto.ErrorResponse(c, dto.NewBadRequestError(errors.New("not compose repository")))
		return
	}

	var req dto.GetComposeServicesRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		span.SetStatus(codes.Error, "failed to bind query parameters")
		span.RecordError(err)
		dto.ErrorResponse(c, dto.NewBadRequestError(err))
		return
	}
	if err := validator.Validate(req); err != nil {
		span.SetStatus(codes.Error, "failed to validate request")
		span.RecordError(err)
		dto.ErrorResponse(c, dto.NewBadRequestError(err))
		return
	}
	req.RepoID = repoID

	resp, err := h.repoUseCase.GetListComposeServices(ctx, req)
	if err != nil {
		span.SetStatus(codes.Error, "failed to get compose services")
		span.RecordError(err)
		dto.ErrorResponse(c, dto.NewErrorFromUsecase(err))
		return
	}

	span.AddEvent("repository created successfully")
	span.SetStatus(codes.Ok, "repository created successfully")
	c.JSON(http.StatusOK, resp)
}

// ListEFS godoc
//
//	@Summary	List EFS resources
//	@Tags		Repository Kompose
//	@Accept		json
//	@Produce	json
//	@Param		repo_type	path		string		true	"Repository type"	Enums(spaces, datasets, models, composes)
//	@Param		namespace	path		string		true	"Repository namespace"
//	@Param		repo_name	path		string		true	"Repository name"
//	@Success	200			{array}		dto.EFS		"Ok"
//	@Failure	400			{object}	dto.HTTPError	"Bad Request - invalid request"
//	@Failure	404			{object}	dto.HTTPError	"Not Found"
//	@Failure	500			{object}	dto.HTTPError	"Internal Server Error"
//	@Router		/repositories/{repo_type}/{namespace}/{repo_name}/pvcs [get]
//
//	@Security	Bearer
func (h *repositoryHandlerImpl) ListEFS(c *gin.Context) {
	ctx, span := oteltrace.Tracer.Start(c.Request.Context(), "handlers.repository_kompose.ListEFS")
	defer span.End()

	repoID, err := types.RepoID{}.FromGinContext(c)
	if err != nil {
		span.SetStatus(codes.Error, "invalid repository ID")
		span.RecordError(err)
		dto.ErrorResponse(c, dto.NewBadRequestError(err, err.Error()))
		return
	}

	if *repoID.RepoType() != enums.RepoType_Composes {
		span.SetStatus(codes.Error, "not compose repository")
		span.RecordError(errors.New("not compose repository"))
		dto.ErrorResponse(c, dto.NewBadRequestError(errors.New("not compose repository")))
		return
	}

	efsList, err := h.composeUseCase.ListEFS(ctx, repoID)
	if err != nil {
		span.SetStatus(codes.Error, "failed to list EFS resources")
		span.RecordError(err)
		dto.ErrorResponse(c, dto.NewErrorFromUsecase(err))
		return
	}

	span.AddEvent("EFS resources listed successfully")
	span.SetStatus(codes.Ok, "EFS resources listed successfully")
	c.JSON(http.StatusOK, dto.ListEFSResponse{
		Data: &efsList,
	})
}
